flowchart TD
    A[Program Start] --> B[Initialize System]
    B --> C[Create Directories<br/>datasheets/, 3d/, emoji_images/, help_files/]
    C --> D[Download Emoji Images<br/>25 colored PNG files]
    D --> E[Load Configuration Files<br/>CSV, JSON databases]
    E --> F[Create GUI<br/>Colored emoji buttons & text area]
    F --> G[Ready State<br/>Waiting for user input]
    
    G --> H{User Action?}
    
    H -->|Single Search| I[Enter Manufacturer & Part Number]
    H -->|Excel Batch| J[Load Excel File]
    H -->|Help| K[Show Help System]
    H -->|Knowledge| L[Display Knowledge Base]
    H -->|Edit Websites| M[Open CSV Editor]
    
    I --> N[🔍 Search Component Clicked]
    N --> O[Start Datasheet Search]
    
    O --> P[Check Manufacturer in CSV]
    P --> Q{Known Website?}
    Q -->|No| R[Google Search for Website]
    Q -->|Yes| S[Search Digi-Key API]
    R --> T[Save Website to CSV]
    T --> S
    
    S --> U{Datasheet Found?}
    U -->|Yes| V[Download PDF to datasheets/]
    U -->|No| W[Search Mouser API]
    
    W --> X{Datasheet Found?}
    X -->|Yes| V
    X -->|No| Y[Search Manufacturer Website]
    
    Y --> Z{Datasheet Found?}
    Z -->|Yes| V
    Z -->|No| AA[Learning Mode<br/>User Manual Search]
    
    V --> BB[Extract Package Type]
    AA --> CC[Learn Pattern & Download]
    CC --> BB
    BB --> DD[Start 3D Model Search]
    
    DD --> EE[Search Manufacturer Website]
    EE --> FF{3D Model Found?}
    FF -->|Yes| GG[Download STEP to 3d/]
    FF -->|No| HH[Search UltraLibrarian]
    
    HH --> II{3D Model Found?}
    II -->|Yes| GG
    II -->|No| JJ[Search SnapMagic/SnapEDA]
    
    JJ --> KK{3D Model Found?}
    KK -->|Yes| GG
    KK -->|No| LL[Search SamacSys]
    
    LL --> MM{3D Model Found?}
    MM -->|Yes| GG
    MM -->|No| NN{WURTH Part?}
    
    NN -->|Yes| OO[Search we-online.com]
    NN -->|No| PP[Learning Mode<br/>User Manual Search]
    
    OO --> QQ{3D Model Found?}
    QQ -->|Yes| GG
    QQ -->|No| PP
    
    PP --> RR[Learn Pattern & Download]
    RR --> GG
    GG --> SS[Log Results to CSV]
    SS --> TT[Update Knowledge Base]
    TT --> UU[Display Results with Colored Emojis]
    UU --> G
    
    J --> VV[Read Excel Columns F & G<br/>Manufacturer & Part Number]
    VV --> WW[🔍 Excel Part File Search Clicked]
    WW --> XX[Process Each Row]
    XX --> YY{More Rows?}
    YY -->|Yes| ZZ[Check Column H & K<br/>Missing files?]
    YY -->|No| AAA[Excel Processing Complete]
    
    ZZ --> BBB{Missing Datasheet?}
    BBB -->|Yes| O
    BBB -->|No| CCC{Missing 3D Model?}
    CCC -->|Yes| DD
    CCC -->|No| XX
    
    AAA --> DDD[Update Excel File<br/>Columns H, J, K]
    DDD --> EEE[Save Excel File]
    EEE --> G
    
    K --> FFF[Display Help Tabs<br/>Quick Start, Learning, Excel, etc.]
    FFF --> G
    
    L --> GGG[Show Manufacturer Knowledge<br/>URLs, Patterns, Success Rates]
    GGG --> G
    
    M --> HHH[Open actual-web-site-xref.csv<br/>in Default Application]
    HHH --> G
    
    style A fill:#e1f5fe
    style G fill:#c8e6c9
    style O fill:#fff3e0
    style DD fill:#f3e5f5
    style AA fill:#ffebee
    style PP fill:#ffebee
    style UU fill:#e8f5e8
    style AAA fill:#e8f5e8