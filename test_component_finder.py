#!/usr/bin/env python3
"""
Automated test to identify exactly where the datasheet finder hangs
"""

import sys
import traceback
import threading
import time

def test_external_datasheet_finder():
    """Test the external datasheet finder directly"""
    print("🔍 Testing external datasheet finder directly...")

    try:
        # Import the external datasheet finder
        print("1. Importing datasheet_finder...")
        from datasheet_finder import find_datasheet_combined
        print("   ✅ Import successful")

        # Test parameters
        manufacturer = "Murata"
        part_number = "GCM033R71A103KA03D"
        search_options = {
            'digikey': True,
            'mouser': False,
            'rs_components': False,
            'generic_web': False
        }

        print(f"2. Testing with: {manufacturer} {part_number}")
        print(f"   Search options: {search_options}")

        # Create a timeout mechanism
        result = [None]  # Use list to allow modification in nested function
        exception = [None]

        def run_finder():
            try:
                print("   🚀 Calling find_datasheet_combined...")
                result[0] = find_datasheet_combined(manufacturer, part_number, search_options)
                print("   ✅ find_datasheet_combined completed successfully")
            except Exception as e:
                exception[0] = e
                print(f"   ❌ find_datasheet_combined failed: {e}")

        # Run in separate thread with timeout
        thread = threading.Thread(target=run_finder)
        thread.daemon = True
        thread.start()

        # Wait for completion or timeout
        timeout_seconds = 30
        print(f"   ⏱️ Waiting up to {timeout_seconds} seconds...")

        for i in range(timeout_seconds):
            if not thread.is_alive():
                break
            time.sleep(1)
            if i % 5 == 4:  # Print every 5 seconds
                print(f"   ⏳ Still waiting... ({i+1}s)")

        if thread.is_alive():
            print(f"   ❌ TIMEOUT after {timeout_seconds} seconds - function is hanging!")
            print("   🔍 The hang is in the external datasheet finder")
            return False
        elif exception[0]:
            print(f"   ❌ Exception occurred: {exception[0]}")
            traceback.print_exc()
            return False
        else:
            print(f"   ✅ Success! Result: {result[0]}")
            return True

    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        traceback.print_exc()
        return False

def test_specific_digikey_function():
    """Test the specific Digikey function that might be hanging"""
    print("\n🔍 Testing Digikey datasheet function directly...")

    try:
        # Import the specific Digikey module
        print("1. Importing digikey_datasheet_improved...")
        import digikey_datasheet_improved
        print("   ✅ Import successful")

        # Test the main function
        manufacturer = "Murata"
        part_number = "GCM033R71A103KA03D"

        print(f"2. Testing Digikey search for: {manufacturer} {part_number}")

        # Create timeout mechanism
        result = [None]
        exception = [None]

        def run_digikey():
            try:
                print("   🚀 Calling Digikey datasheet search...")
                # Check if there's a main search function
                if hasattr(digikey_datasheet_improved, 'search_digikey_datasheet'):
                    result[0] = digikey_datasheet_improved.search_digikey_datasheet(manufacturer, part_number)
                elif hasattr(digikey_datasheet_improved, 'main'):
                    result[0] = digikey_datasheet_improved.main()
                else:
                    print("   ⚠️ No recognizable main function found")
                    result[0] = "No main function"
                print("   ✅ Digikey search completed")
            except Exception as e:
                exception[0] = e
                print(f"   ❌ Digikey search failed: {e}")

        # Run with timeout
        thread = threading.Thread(target=run_digikey)
        thread.daemon = True
        thread.start()

        timeout_seconds = 20
        print(f"   ⏱️ Waiting up to {timeout_seconds} seconds...")

        for i in range(timeout_seconds):
            if not thread.is_alive():
                break
            time.sleep(1)
            if i % 5 == 4:
                print(f"   ⏳ Still waiting... ({i+1}s)")

        if thread.is_alive():
            print(f"   ❌ TIMEOUT - Digikey function is hanging!")
            return False
        elif exception[0]:
            print(f"   ❌ Exception: {exception[0]}")
            return False
        else:
            print(f"   ✅ Success! Result: {result[0]}")
            return True

    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False
                    
                    # Create mock root window
                    mock_root = MagicMock()
                    mock_tk.return_value = mock_root
                    
                    print("3. Creating ComponentFinderGUI instance...")

                    # Create the component finder instance with mock root
                    finder = component_finder.ComponentFinderGUI(mock_root)
                    print("   ✅ ComponentFinderGUI created")
                    
                    # Test the process_matrix_row function directly
                    print("4. Testing process_matrix_row function...")
                    
                    # Create mock Excel data
                    test_data = {
                        'Manufacturer Name ': ['Murata'],
                        'Manufacturer full part number': ['GCM155R71H104KE02D'],
                        'Datasheet': [''],
                        'STEP Source': [''],
                        'STEP File': ['']
                    }
                    
                    finder.excel_df = pd.DataFrame(test_data)
                    finder.column_mapping = {
                        'manufacturer': 'Manufacturer Name ',
                        'part_number': 'Manufacturer full part number',
                        'datasheet': 'Datasheet',
                        'step_source': 'STEP Source',
                        'step_file': 'STEP File'
                    }
                    
                    # Mock the search options (all disabled)
                    finder.search_datasheet_digikey = MagicMock()
                    finder.search_datasheet_digikey.get.return_value = True  # Enable Digikey
                    finder.search_datasheet_mouser = MagicMock()
                    finder.search_datasheet_mouser.get.return_value = False
                    finder.search_datasheet_rs = MagicMock()
                    finder.search_datasheet_rs.get.return_value = False
                    finder.search_datasheet_generic = MagicMock()
                    finder.search_datasheet_generic.get.return_value = False
                    
                    # Mock 3D search options (all disabled)
                    finder.search_3d_manufacturer = MagicMock()
                    finder.search_3d_manufacturer.get.return_value = False
                    finder.search_3d_ultralibrarian = MagicMock()
                    finder.search_3d_ultralibrarian.get.return_value = False
                    finder.search_3d_samacsys = MagicMock()
                    finder.search_3d_samacsys.get.return_value = False
                    finder.search_3d_snapeda = MagicMock()
                    finder.search_3d_snapeda.get.return_value = False
                    
                    # Mock the add_comment function to track progress
                    comments = []
                    def mock_add_comment(message):
                        comments.append(f"{len(comments)+1:3d}. {message}")
                        print(f"   Line {len(comments):3d}: {message}")
                        
                        # Check if we're at line 99 or close to it
                        if len(comments) >= 95:
                            print(f"   ⚠️ Approaching line 99, current line: {len(comments)}")
                    
                    finder.add_comment = mock_add_comment
                    
                    # Mock other functions that might cause hanging
                    finder.update_matrix_cell = MagicMock()
                    finder.search_datasheet_enhanced = MagicMock()
                    finder.search_datasheet_enhanced.return_value = {
                        'success': True,
                        'source': 'Digikey',
                        'datasheet_file': 'test.pdf',
                        'pdf_parsing': {
                            'part_validation': {'valid': False},
                            'package_info': {'package': None}
                        }
                    }
                    
                    print("5. Running process_matrix_row...")
                    
                    # Run the function
                    result = finder.process_matrix_row(0, 'Murata', 'GCM155R71H104KE02D')
                    
                    print("6. Test completed successfully!")
                    print(f"   Total comments: {len(comments)}")
                    print("   Last 5 comments:")
                    for comment in comments[-5:]:
                        print(f"     {comment}")
                    
                    return True
                    
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        print("Full traceback:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting automated hang detection tests...")
    print("=" * 60)

    # Test 1: External datasheet finder
    success1 = test_external_datasheet_finder()

    print("\n" + "=" * 60)

    # Test 2: Specific Digikey function
    success2 = test_specific_digikey_function()

    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    print(f"   External datasheet finder: {'✅ PASS' if success1 else '❌ HANG/FAIL'}")
    print(f"   Digikey specific function: {'✅ PASS' if success2 else '❌ HANG/FAIL'}")

    if success1 and success2:
        print("\n✅ All tests passed - no hanging detected")
    else:
        print("\n❌ HANGING DETECTED - identified the problematic function")
        if not success1:
            print("   🎯 Problem is in the external datasheet finder")
        if not success2:
            print("   🎯 Problem is in the Digikey specific function")

    print("=" * 60)
