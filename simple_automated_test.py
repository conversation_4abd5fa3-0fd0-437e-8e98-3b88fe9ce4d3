#!/usr/bin/env python3
"""
Simple automated test - directly test external functions
"""

import pandas as pd
import sys
import traceback
import threading
import time

def load_spreadsheet_parts():
    """Load all parts from the spreadsheet"""
    try:
        excel_file = "Teledyne_Flir_master-footprint_list-tura.xlsx"
        df = pd.read_excel(excel_file)
        
        manufacturer_col = 'Manufacturer Name '
        part_col = 'Manufacturer full part number'
        
        parts = []
        for idx, row in df.iterrows():
            mfg = str(row[manufacturer_col]).strip() if pd.notna(row[manufacturer_col]) else ""
            part = str(row[part_col]).strip() if pd.notna(row[part_col]) else ""
            
            if mfg and part and mfg != 'nan' and part != 'nan':
                parts.append({
                    'row': idx + 1,
                    'manufacturer': mfg,
                    'part_number': part
                })
        
        return parts
    except Exception as e:
        print(f"❌ Error loading spreadsheet: {e}")
        return []

def test_datasheet_finder(manufacturer, part_number):
    """Test the datasheet finder for a part"""
    try:
        from datasheet_finder import find_datasheet_combined
        
        print(f"   📄 Testing datasheet finder...")
        result = find_datasheet_combined(manufacturer, part_number, interactive=False)
        
        if result and result.get('success'):
            source = result.get('source', 'Unknown')
            datasheet_file = result.get('datasheet_file', '')
            print(f"   ✅ Datasheet found via {source}")
            if datasheet_file:
                print(f"      📁 File: {datasheet_file}")
            
            # Check PDF parsing
            if result.get('pdf_parsing'):
                pdf_info = result['pdf_parsing']
                if pdf_info.get('part_validation'):
                    valid = pdf_info['part_validation'].get('valid', False)
                    print(f"      🔍 Part validation: {'✅ Valid' if valid else '❌ Invalid'}")
                if pdf_info.get('package_info'):
                    package = pdf_info['package_info'].get('package')
                    if package:
                        print(f"      📦 Package: {package}")
            
            return True, source, result
        else:
            message = result.get('message', 'No message') if result else 'No result'
            print(f"   ❌ Datasheet not found: {message}")
            return False, None, result
            
    except Exception as e:
        print(f"   ❌ Datasheet finder error: {e}")
        return False, None, None

def test_step_finder(manufacturer, part_number):
    """Test the STEP finder for a part"""
    try:
        from step_finder import find_step_file
        
        print(f"   🎯 Testing STEP finder...")
        
        # Create search options (all enabled)
        search_options = {
            'manufacturer': True,
            'ultralibrarian': True,
            'samacsys': True,
            'snapeda': True
        }
        
        result = find_step_file(manufacturer, part_number, search_options)
        
        if result and result.get('success'):
            source = result.get('source', 'Unknown')
            step_file = result.get('step_file', '')
            print(f"   ✅ STEP file found via {source}")
            if step_file:
                print(f"      📁 File: {step_file}")
            return True, source, result
        else:
            message = result.get('message', 'No message') if result else 'No result'
            print(f"   ❌ STEP file not found: {message}")
            return False, None, result
            
    except Exception as e:
        print(f"   ❌ STEP finder error: {e}")
        return False, None, None

def test_part_with_timeout(part, timeout=60):
    """Test a single part with timeout"""
    results = {
        'part': part,
        'datasheet_found': False,
        'datasheet_source': None,
        'step_found': False,
        'step_source': None,
        'status': 'UNKNOWN',
        'errors': []
    }
    
    def run_tests():
        try:
            # Test datasheet finder
            ds_found, ds_source, ds_result = test_datasheet_finder(part['manufacturer'], part['part_number'])
            results['datasheet_found'] = ds_found
            results['datasheet_source'] = ds_source
            
            # Test STEP finder
            step_found, step_source, step_result = test_step_finder(part['manufacturer'], part['part_number'])
            results['step_found'] = step_found
            results['step_source'] = step_source
            
            results['status'] = 'COMPLETED'
            
        except Exception as e:
            results['errors'].append(str(e))
            results['status'] = 'ERROR'
    
    # Run with timeout
    thread = threading.Thread(target=run_tests)
    thread.daemon = True
    thread.start()
    
    # Wait for completion or timeout
    for i in range(timeout):
        if not thread.is_alive():
            break
        time.sleep(1)
        if i % 15 == 14:  # Print every 15 seconds
            print(f"   ⏳ Still testing... ({i+1}s)")
    
    if thread.is_alive():
        print(f"   ❌ TIMEOUT after {timeout}s")
        results['status'] = 'TIMEOUT'
        results['errors'].append(f'Timeout after {timeout}s')
    
    return results

def main():
    """Main test function"""
    print("🚀 SIMPLE AUTOMATED TEST - ALL EXTERNAL FUNCTIONS")
    print("=" * 80)
    
    # Load parts
    parts = load_spreadsheet_parts()
    if not parts:
        print("❌ No parts loaded from spreadsheet")
        return False
    
    print(f"📊 Loaded {len(parts)} parts from spreadsheet")
    for part in parts:
        print(f"   Row {part['row']:2d}: {part['manufacturer']} {part['part_number']}")
    
    print("\n" + "=" * 80)
    print("🧪 TESTING ALL PARTS...")
    print("=" * 80)
    
    # Test each part
    all_results = []
    for i, part in enumerate(parts, 1):
        print(f"\n{'='*15} PART {i}/{len(parts)} {'='*15}")
        print(f"🔍 Testing: {part['manufacturer']} {part['part_number']}")
        print("-" * 50)
        
        result = test_part_with_timeout(part, timeout=60)
        all_results.append(result)
        
        # Show summary for this part
        print(f"   📊 Summary:")
        print(f"      Status: {result['status']}")
        print(f"      Datasheet: {'✅' if result['datasheet_found'] else '❌'} {result['datasheet_source'] or 'Not found'}")
        print(f"      STEP File: {'✅' if result['step_found'] else '❌'} {result['step_source'] or 'Not found'}")
        if result['errors']:
            print(f"      Errors: {len(result['errors'])}")
    
    # Final summary
    print("\n" + "=" * 80)
    print("📊 FINAL SUMMARY")
    print("=" * 80)
    
    completed = sum(1 for r in all_results if r['status'] == 'COMPLETED')
    timeouts = sum(1 for r in all_results if r['status'] == 'TIMEOUT')
    errors = sum(1 for r in all_results if r['status'] == 'ERROR')
    
    datasheets_found = sum(1 for r in all_results if r['datasheet_found'])
    steps_found = sum(1 for r in all_results if r['step_found'])
    
    print(f"📈 Processing Status:")
    print(f"   ✅ Completed: {completed}/{len(parts)} ({completed/len(parts)*100:.1f}%)")
    print(f"   ⏱️ Timeouts: {timeouts}/{len(parts)} ({timeouts/len(parts)*100:.1f}%)")
    print(f"   ❌ Errors: {errors}/{len(parts)} ({errors/len(parts)*100:.1f}%)")
    
    print(f"\n📈 Success Rates:")
    print(f"   📄 Datasheets found: {datasheets_found}/{len(parts)} ({datasheets_found/len(parts)*100:.1f}%)")
    print(f"   🎯 STEP files found: {steps_found}/{len(parts)} ({steps_found/len(parts)*100:.1f}%)")
    
    # Detailed results
    print(f"\n📋 DETAILED RESULTS:")
    for result in all_results:
        part = result['part']
        status_icon = "✅" if result['status'] == 'COMPLETED' else "❌"
        datasheet_icon = "📄" if result['datasheet_found'] else "❌"
        step_icon = "🎯" if result['step_found'] else "❌"
        
        print(f"   {status_icon} Row {part['row']:2d}: {part['manufacturer']} {part['part_number']}")
        print(f"      {datasheet_icon} Datasheet: {result['datasheet_source'] or 'Not found'}")
        print(f"      {step_icon} STEP: {result['step_source'] or 'Not found'}")
        if result['errors']:
            print(f"      ❌ Errors: {len(result['errors'])}")
    
    # Check for 3D model finding issues
    print(f"\n🎯 3D MODEL ANALYSIS:")
    if steps_found == 0:
        print("   ❌ NO STEP FILES FOUND - NEED TO INVESTIGATE 3D FINDER")
    elif steps_found < len(parts) // 2:
        print(f"   ⚠️ LOW SUCCESS RATE ({steps_found}/{len(parts)}) - SOME ISSUES WITH 3D FINDER")
    else:
        print(f"   ✅ GOOD SUCCESS RATE ({steps_found}/{len(parts)}) - 3D FINDER WORKING WELL")
    
    return timeouts == 0 and errors == 0

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
    else:
        print(f"\n❌ SOME ISSUES FOUND - CHECK RESULTS ABOVE")
