<mxfile host="Electron" modified="2024-01-01T12:00:00.000Z" agent="draw.io" version="22.1.11">
  <diagram name="Component Finder - Detailed Step-by-Step Flowchart" id="flowchart">
    <mxGraphModel dx="2000" dy="1200" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2000" pageHeight="3000" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- PROGRAM INITIALIZATION -->
        <mxCell id="start" value="__init__()&#xa;Program Start" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="900" y="20" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="setupEmoji" value="_setup_emoji_images()&#xa;Download 25 emoji PNGs&#xa;Create emoji_images/ dir" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="840" y="120" width="160" height="70" as="geometry" />
        </mxCell>

        <mxCell id="createDirs" value="Create Directories:&#xa;datasheets/, 3d/&#xa;help_files/" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="840" y="220" width="160" height="60" as="geometry" />
        </mxCell>

        <mxCell id="loadKnowledge" value="_load_knowledge_base()&#xa;Load manufacturer_knowledge.json&#xa;Load actual-web-site-xref.csv" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="840" y="310" width="160" height="70" as="geometry" />
        </mxCell>

        <mxCell id="setupGUI" value="setup_gui()&#xa;Create colored emoji buttons&#xa;Create text area" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="840" y="410" width="160" height="60" as="geometry" />
        </mxCell>

        <!-- READY STATE -->
        <mxCell id="ready" value="READY STATE&#xa;GUI displayed&#xa;Waiting for user input" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="840" y="510" width="160" height="70" as="geometry" />
        </mxCell>

        <!-- USER ACTION DECISION -->
        <mxCell id="userAction" value="User Clicks Button?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="860" y="620" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- SINGLE COMPONENT SEARCH PATH -->
        <mxCell id="clickSearch" value="🔍 Search Component&#xa;Button Clicked" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="750" width="140" height="50" as="geometry" />
        </mxCell>

        <mxCell id="startSearch" value="start_search()&#xa;Validate inputs&#xa;Disable button, start progress" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="830" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="searchThread" value="Create Thread:&#xa;search_component()&#xa;(manufacturer, part_number)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="930" width="140" height="70" as="geometry" />
        </mxCell>

        <!-- EXCEL BATCH PATH -->
        <mxCell id="clickExcel" value="📊 Load Excel File&#xa;Button Clicked" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1300" y="750" width="140" height="50" as="geometry" />
        </mxCell>

        <mxCell id="loadExcel" value="load_excel_file()&#xa;Read columns F & G&#xa;Enable matrix search mode" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1300" y="830" width="140" height="70" as="geometry" />
        </mxCell>

        <!-- DATASHEET SEARCH PROCESS -->
        <mxCell id="searchComponent" value="search_component()&#xa;Main search orchestrator&#xa;Runs in separate thread" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="1030" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="searchDatasheetEnhanced" value="search_datasheet_enhanced()&#xa;Import datasheet_finder&#xa;Call find_datasheet_combined()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="1130" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="datasheetDecision" value="Datasheet&#xa;Found?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="410" y="1230" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="datasheetFallback" value="search_datasheet_fallback()&#xa;Try Digi-Key API&#xa;Try Mouser API&#xa;Try manufacturer website" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="200" y="1330" width="140" height="80" as="geometry" />
        </mxCell>

        <mxCell id="datasheetLearning" value="learn_datasheet_pattern()&#xa;Open browser for manual search&#xa;Learn download pattern" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="200" y="1450" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="datasheetSuccess" value="Datasheet Downloaded&#xa;Extract package type&#xa;Save to datasheets/" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="600" y="1230" width="140" height="70" as="geometry" />
        </mxCell>

        <!-- 3D MODEL SEARCH PROCESS -->
        <mxCell id="search3DEnhanced" value="search_step_enhanced()&#xa;Import step_finder&#xa;Call find_step_file()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="600" y="1330" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="stepDecision" value="STEP File&#xa;Found?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="610" y="1430" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="stepFallback" value="search_alternative_step_sources()&#xa;Try manufacturer website&#xa;Check for WURTH parts" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="800" y="1530" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="wurthCheck" value="WURTH&#xa;Part?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="810" y="1630" width="120" height="60" as="geometry" />
        </mxCell>

        <mxCell id="wurthSearch" value="search_wurth_simple()&#xa;Use WURTH-specific&#xa;search method" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1000" y="1630" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="manufacturerSearch" value="search_manufacturer_for_step()&#xa;Search manufacturer website&#xa;for STEP files" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="600" y="1730" width="140" height="70" as="geometry" />
        </mxCell>

        <!-- ALTERNATIVE 3D SOURCES -->
        <mxCell id="alternativeSources" value="Try Alternative Sources:&#xa;UltraLibrarian, SnapMagic,&#xa;SamacSys" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="1830" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="ultraLibrarian" value="search_ultralibrarian_&#xa;with_learning()&#xa;Try ultralibrarian.com" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="200" y="1930" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="snapMagic" value="search_snapmagic_&#xa;with_learning()&#xa;Try snapeda.com, snapmagic.com" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="1930" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="samacSys" value="search_samacsys_&#xa;with_learning()&#xa;Try componentsearchengine.com" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="600" y="1930" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="stepLearning" value="Learning Mode:&#xa;search_manufacturer_&#xa;with_learning()&#xa;Open browser for manual search" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="800" y="1930" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- SUCCESS PATHS -->
        <mxCell id="stepSuccess" value="STEP File Downloaded&#xa;Save to 3d/ directory&#xa;Update filename" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1000" y="1430" width="140" height="70" as="geometry" />
        </mxCell>

        <!-- RESULTS LOGGING -->
        <mxCell id="logResults" value="Log to found-files-log.csv:&#xa;- Manufacturer, Part Number&#xa;- Datasheet URL & filename&#xa;- 3D Model URL & filename&#xa;- Package type, Date found" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1000" y="1530" width="140" height="100" as="geometry" />
        </mxCell>

        <mxCell id="updateKnowledge" value="Update Knowledge Base:&#xa;- manufacturer_knowledge.json&#xa;- actual-web-site-xref.csv&#xa;- download_patterns.json" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1000" y="1660" width="140" height="80" as="geometry" />
        </mxCell>

        <mxCell id="displayResults" value="Display Results in GUI:&#xa;- Update status fields&#xa;- Show colored emoji status&#xa;- Enable buttons" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1000" y="1770" width="140" height="70" as="geometry" />
        </mxCell>

        <!-- EXCEL BATCH PROCESSING -->
        <mxCell id="excelSearch" value="🔍 Excel Part File Search&#xa;Button Clicked" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1300" y="930" width="140" height="50" as="geometry" />
        </mxCell>

        <mxCell id="processChoice" value="Process All Parts&#xa;or Single Part?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1310" y="1010" width="120" height="70" as="geometry" />
        </mxCell>

        <mxCell id="processAll" value="process_all_matrix_parts()&#xa;Loop through all rows&#xa;Check columns H & K" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1500" y="1110" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="processSingle" value="matrix_search_component()&#xa;Find part in Excel&#xa;Process single row" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1100" y="1110" width="140" height="70" as="geometry" />
        </mxCell>

        <mxCell id="processRow" value="process_matrix_row()&#xa;Check Column H (datasheet)&#xa;Check Column K (STEP file)&#xa;Update if missing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1300" y="1210" width="140" height="80" as="geometry" />
        </mxCell>

        <mxCell id="updateExcel" value="Update Excel File:&#xa;- Column H: datasheet path&#xa;- Column J: 3D source&#xa;- Column K: STEP filename&#xa;Save Excel file" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1300" y="1320" width="140" height="90" as="geometry" />
        </mxCell>

        <!-- INITIALIZATION FLOW -->
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="start" target="setupEmoji">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="setupEmoji" target="createDirs">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="createDirs" target="loadKnowledge">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="loadKnowledge" target="setupGUI">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="setupGUI" target="ready">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="ready" target="userAction">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- SINGLE COMPONENT SEARCH FLOW -->
        <mxCell id="arrow7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="userAction" target="clickSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="clickSearch" target="startSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="startSearch" target="searchThread">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="searchThread" target="searchComponent">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- DATASHEET SEARCH FLOW -->
        <mxCell id="arrow11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="searchComponent" target="searchDatasheetEnhanced">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="searchDatasheetEnhanced" target="datasheetDecision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetDecision" target="datasheetSuccess">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetDecision" target="datasheetFallback">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetFallback" target="datasheetLearning">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 3D MODEL SEARCH FLOW -->
        <mxCell id="arrow16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetSuccess" target="search3DEnhanced">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="search3DEnhanced" target="stepDecision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepDecision" target="stepSuccess">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepDecision" target="stepFallback">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepFallback" target="wurthCheck">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="wurthCheck" target="wurthSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="wurthCheck" target="manufacturerSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="manufacturerSearch" target="alternativeSources">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- RESULTS FLOW -->
        <mxCell id="arrow24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepSuccess" target="logResults">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="logResults" target="updateKnowledge">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="updateKnowledge" target="displayResults">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- EXCEL BATCH PROCESSING FLOW -->
        <mxCell id="arrow27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="userAction" target="clickExcel">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="clickExcel" target="loadExcel">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="loadExcel" target="excelSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="excelSearch" target="processChoice">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="processChoice" target="processAll">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="processChoice" target="processSingle">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="processAll" target="processRow">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="processSingle" target="processRow">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="processRow" target="updateExcel">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- RETURN TO READY STATE -->
        <mxCell id="arrow36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;entryX=1;entryY=0.5;" edge="1" parent="1" source="displayResults" target="ready">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1200" y="1805" />
              <mxPoint x="1200" y="545" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;entryX=1;entryY=0.5;" edge="1" parent="1" source="updateExcel" target="ready">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1200" y="1365" />
              <mxPoint x="1200" y="545" />
            </Array>
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
