<!DOCTYPE html>
    <html class="react-root">    
      <head>
        <link rel="stylesheet" type="text/css" href="/assets/static/src_styles_global-00f5cfaf.JomUJUuC.css">
        <link rel="stylesheet" type="text/css" href="/assets/static/src_styles_footer_cn-e9255475.C6VRb3Zo.css">
        <link rel="stylesheet" type="text/css" href="/assets/static/src_styles_jquery.qtip.C0pyK8Sv.css">
        <link rel="stylesheet" type="text/css" href="/assets/static/src_styles_simgraph-2e2e66e0.xdRgJchD.css">
        <!-- OneTrust Cookies Consent Notice start -->
        <script type="text/javascript" src="https://cdn.cookielaw.org/consent/b866f587-e7d0-4f41-bb98-ffe290290472/OtAutoBlock.js"></script>
        <script src="https://cdn.cookielaw.org/scripttemplates/otSDKStub.js"  type="text/javascript" charset="UTF-8" data-domain-script="b866f587-e7d0-4f41-bb98-ffe290290472"></script>
        <script type="text/javascript">
        function OptanonWrapper() { }
        </script>
        <script type="text/javascript">
        window.addEventListener("OTConsentApplied", function(e) {
          document.location.reload();
        }, { once: true });
        </script>
        <!-- OneTrust Cookies Consent Notice end -->
        
        <script src="https://assets.adobedtm.com/193a95d152ddc4b48eec9ef739f5e8baea85bc42/satelliteLib-cd45bb235be3c8a9435191c7064254804b40eb59.js" charset="utf-8"></script>

        <!-- Google Tag Manager -->
        <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-PRTZ8QW');</script>
        <!-- End Google Tag Manager -->
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">
        <link
          rel="stylesheet"
          href="/styles/SiteRenewal/Business/header.css"
          data-precedence="high"
        />

        <link rel="stylesheet" href="/styles/SiteRenewal/Business/layout.css" data-precedence="high">
        <noscript>
          <link rel="stylesheet" href="/styles/SiteRenewal/Business/noscript.css" />
        </noscript>
      </head>
      <body>
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PRTZ8QW"
        height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->
        <div id="react-root"><!--$!--><template></template><!--/$--></div>
        <script type="text/javascript">if (typeof _satellite !== "undefined") { _satellite.pageBottom();}</script>
        <script id="vike_pageContext" type="application/json">{"pageProps":"!undefined","locale":"en-us","abortReason":"!undefined","_urlRewrite":null,"_urlRedirect":"!undefined","abortStatusCode":"!undefined","_abortCall":"!undefined","_pageContextInitIsPassedToClient":"!undefined","pageId":"/src/pages/pim/details","routeParams":{},"data":"!undefined"}</script>
        <script src="/assets/entries/entry-client-routing.AUDkrL8M.js" type="module" async></script>
        <link rel="modulepreload" href="/assets/entries/src_pages_pim_details.HCR_2AKA.js" as="script" type="text/javascript">
        <link rel="modulepreload" href="/assets/chunks/chunk-YyWLr7m6.js" as="script" type="text/javascript">
        <link rel="modulepreload" href="/assets/chunks/chunk-D2NCWl18.js" as="script" type="text/javascript">
        <link rel="modulepreload" href="/assets/chunks/chunk-CJFZ8k8k.js" as="script" type="text/javascript">
      </body>
    </html>