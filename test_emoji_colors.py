#!/usr/bin/env python3
"""
Test script to verify if tkin<PERSON> can display colored emojis on Windows
This will definitively test if colored emoji display is possible
"""

import tkinter as tk
from tkinter import scrolledtext, font
import platform

def test_emoji_colors():
    """Test if emojis display in color in tkinter"""
    
    root = tk.Tk()
    root.title("Emoji Color Test")
    root.geometry("600x400")
    
    system = platform.system()
    print(f"Testing on: {system}")
    
    # Test different font configurations
    test_fonts = []
    
    if system == "Windows":
        test_fonts = [
            ('Segoe UI Emoji', 14),
            ('Microsoft YaHei UI', 14),
            ('Segoe UI Symbol', 14),
            ('Arial Unicode MS', 14),
            ('Segoe UI', 14)
        ]
    elif system == "Darwin":
        test_fonts = [
            ('Apple Color Emoji', 14),
            ('SF Pro Display', 14)
        ]
    else:
        test_fonts = [
            ('Noto Color Emoji', 14),
            ('DejaVu Sans', 14)
        ]
    
    # Test emojis
    test_emojis = "🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠 🎯 💡 🔧 ⭐"
    
    # Create text widgets for each font
    for i, (font_family, font_size) in enumerate(test_fonts):
        try:
            # Test if font exists
            test_font = tk.font.Font(family=font_family, size=font_size)
            
            # Create label
            label = tk.Label(root, text=f"Font: {font_family}", font=('Arial', 10, 'bold'))
            label.pack(pady=5)
            
            # Create text widget
            text_widget = tk.Text(root, height=2, width=60, font=(font_family, font_size))
            text_widget.pack(pady=2)
            
            # Insert test emojis
            text_widget.insert(tk.END, f"Test: {test_emojis}\n")
            text_widget.insert(tk.END, "👆 Are these emojis colored? (not black & white)")
            text_widget.config(state='disabled')
            
            print(f"✅ Created test with font: {font_family}")
            
        except Exception as e:
            print(f"❌ Font {font_family} failed: {e}")
            continue
    
    # Add instructions
    instruction_label = tk.Label(root, 
                                text="Look at the emojis above. If ANY are in color (not black & white), tkinter CAN display colored emojis!",
                                font=('Arial', 10, 'bold'),
                                fg='red',
                                wraplength=500)
    instruction_label.pack(pady=10)
    
    # Add result buttons
    def report_colored():
        print("✅ USER REPORTS: Emojis ARE colored!")
        print("✅ RESULT: tkinter CAN display colored emojis on this system")
        root.quit()
    
    def report_black_white():
        print("❌ USER REPORTS: Emojis are black & white only")
        print("❌ RESULT: tkinter CANNOT display colored emojis on this system")
        root.quit()
    
    button_frame = tk.Frame(root)
    button_frame.pack(pady=10)
    
    colored_btn = tk.Button(button_frame, text="✅ I see COLORED emojis", 
                           command=report_colored, bg='green', fg='white', font=('Arial', 10, 'bold'))
    colored_btn.pack(side=tk.LEFT, padx=10)
    
    bw_btn = tk.Button(button_frame, text="❌ Only BLACK & WHITE emojis", 
                      command=report_black_white, bg='red', fg='white', font=('Arial', 10, 'bold'))
    bw_btn.pack(side=tk.LEFT, padx=10)
    
    print("🔍 Emoji color test window opened")
    print("👀 Please look at the emojis and click the appropriate button")
    
    root.mainloop()

if __name__ == "__main__":
    test_emoji_colors()
