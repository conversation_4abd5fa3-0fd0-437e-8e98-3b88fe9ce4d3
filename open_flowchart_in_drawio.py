#!/usr/bin/env python3
"""
Script to help open the Component Finder flowchart in Draw.io Desktop
"""

import os
import subprocess
import webbrowser
from pathlib import Path
import winreg

def find_drawio_executable():
    """Find the Draw.io Desktop executable"""
    print("🔍 Looking for Draw.io Desktop...")
    
    # Common installation paths
    common_paths = [
        Path(os.environ.get('LOCALAPPDATA', '')) / 'Programs' / 'drawio' / 'draw.io.exe',
        Path(os.environ.get('PROGRAMFILES', '')) / 'draw.io' / 'draw.io.exe',
        Path(os.environ.get('PROGRAMFILES(X86)', '')) / 'draw.io' / 'draw.io.exe',
        Path(os.environ.get('APPDATA', '')) / 'draw.io' / 'draw.io.exe'
    ]
    
    for path in common_paths:
        if path.exists():
            print(f"✅ Found Draw.io at: {path}")
            return str(path)
    
    print("⚠️ Draw.io executable not found in common locations")
    return None

def find_flowchart_files():
    """Find the flowchart files in the current directory"""
    print("📁 Looking for flowchart files...")
    
    files_to_find = [
        "component_finder_flowchart.mmd",
        "Component_Finder_Flowchart.md",
        "Component_Finder_Flowchart.txt"
    ]
    
    found_files = []
    
    for filename in files_to_find:
        file_path = Path(filename)
        if file_path.exists():
            print(f"✅ Found: {filename}")
            found_files.append(str(file_path.absolute()))
        else:
            print(f"❌ Not found: {filename}")
    
    return found_files

def show_file_contents(file_path):
    """Show the contents of the flowchart file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # If it's a markdown file, extract just the mermaid code
        if file_path.endswith('.md'):
            start_marker = "```mermaid"
            end_marker = "```"
            
            start_idx = content.find(start_marker)
            if start_idx != -1:
                start_idx += len(start_marker)
                end_idx = content.find(end_marker, start_idx)
                if end_idx != -1:
                    mermaid_code = content[start_idx:end_idx].strip()
                    print(f"\n📄 Mermaid code from {Path(file_path).name}:")
                    print("=" * 50)
                    print(mermaid_code[:500] + "..." if len(mermaid_code) > 500 else mermaid_code)
                    return mermaid_code
        else:
            print(f"\n📄 Contents of {Path(file_path).name}:")
            print("=" * 50)
            print(content[:500] + "..." if len(content) > 500 else content)
            return content
            
    except Exception as e:
        print(f"❌ Error reading {file_path}: {e}")
        return None

def launch_drawio_with_instructions():
    """Launch Draw.io and provide import instructions"""
    drawio_path = find_drawio_executable()
    
    if drawio_path:
        try:
            print("🚀 Launching Draw.io Desktop...")
            subprocess.Popen([drawio_path])
            print("✅ Draw.io Desktop launched!")
            return True
        except Exception as e:
            print(f"❌ Failed to launch Draw.io: {e}")
            return False
    else:
        print("❌ Draw.io executable not found")
        print("💡 Try opening Draw.io from Start Menu or Desktop")
        return False

def copy_mermaid_to_clipboard(mermaid_code):
    """Copy the mermaid code to clipboard"""
    try:
        import pyperclip
        pyperclip.copy(mermaid_code)
        print("✅ Mermaid code copied to clipboard!")
        return True
    except ImportError:
        print("⚠️ pyperclip not available - cannot copy to clipboard")
        return False
    except Exception as e:
        print(f"❌ Failed to copy to clipboard: {e}")
        return False

def provide_import_instructions():
    """Provide step-by-step instructions for importing into Draw.io"""
    print("\n🎯 How to Import Your Flowchart into Draw.io:")
    print("=" * 60)
    print("1. 📂 In Draw.io Desktop, click 'Create New Diagram'")
    print("2. 📝 Choose any template (you'll replace it)")
    print("3. 📥 Go to: File → Import From → Text...")
    print("4. 🔧 In the dialog:")
    print("   - Select 'Mermaid' from the format dropdown")
    print("   - Paste your flowchart code (Ctrl+V)")
    print("   - Click 'Import'")
    print("5. ✨ Your flowchart will appear as an editable diagram!")
    print("6. 💾 Save as: File → Save As → choose location and format")
    print("7. 📤 Export as PDF: File → Export as → PDF")
    
    print("\n🎨 Draw.io Features You Can Use:")
    print("- Drag and drop to rearrange elements")
    print("- Change colors, fonts, and styles")
    print("- Add new shapes and connectors")
    print("- Export to PDF, PNG, SVG, VSDX")
    print("- Professional styling options")

def main():
    """Main function to help open flowchart in Draw.io"""
    print("🎨 Draw.io Flowchart Opener")
    print("=" * 50)
    
    # Find flowchart files
    flowchart_files = find_flowchart_files()
    
    if not flowchart_files:
        print("❌ No flowchart files found!")
        print("💡 Make sure you're in the correct directory")
        print("📁 Looking for: component_finder_flowchart.mmd or Component_Finder_Flowchart.md")
        return
    
    # Show available files
    print(f"\n📋 Found {len(flowchart_files)} flowchart file(s):")
    for i, file_path in enumerate(flowchart_files, 1):
        print(f"   {i}. {Path(file_path).name}")
    
    # Use the .mmd file if available (it's the cleanest)
    preferred_file = None
    for file_path in flowchart_files:
        if file_path.endswith('.mmd'):
            preferred_file = file_path
            break
    
    if not preferred_file:
        preferred_file = flowchart_files[0]
    
    print(f"\n📄 Using file: {Path(preferred_file).name}")
    
    # Show file contents and copy to clipboard
    mermaid_code = show_file_contents(preferred_file)
    
    if mermaid_code:
        copy_mermaid_to_clipboard(mermaid_code)
    
    # Launch Draw.io
    print("\n🚀 Launching Draw.io Desktop...")
    if launch_drawio_with_instructions():
        provide_import_instructions()
        
        print(f"\n📁 Your flowchart files are located at:")
        for file_path in flowchart_files:
            print(f"   📄 {file_path}")
    else:
        print("\n💡 Manual Launch:")
        print("1. Open Draw.io Desktop from Start Menu")
        print("2. Follow the import instructions above")
        provide_import_instructions()

if __name__ == "__main__":
    main()
