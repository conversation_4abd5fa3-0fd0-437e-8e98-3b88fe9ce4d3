#!/usr/bin/env python3
"""
Test the full workflow to identify and fix all issues
"""

import pandas as pd
import sys
import traceback
from unittest.mock import patch, MagicMock
import tkinter as tk

def test_component_finder_workflow():
    """Test the complete workflow with mocked GUI"""
    print("🧪 TESTING COMPLETE COMPONENT FINDER WORKFLOW")
    print("=" * 60)
    
    try:
        # Import component finder
        import component_finder
        
        # Create a real tkinter root for testing
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        
        # Create the component finder
        finder = component_finder.ComponentFinderGUI(root)
        
        # Set up test data
        test_data = {
            'Manufacturer Name ': ['Murata'],
            'Manufacturer full part number': ['GCM155R71H104KE02D'],
            'Datasheet': [''],
            'STEP Source': [''],
            'STEP File': ['']
        }
        
        finder.excel_df = pd.DataFrame(test_data)
        finder.column_mapping = {
            'manufacturer': 'Manufacturer Name ',
            'part_number': 'Manufacturer full part number',
            'datasheet': 'Datasheet',
            'step_source': 'STEP Source',
            'step_file': 'STEP File'
        }
        
        # Enable all search options
        finder.search_datasheet_digikey.set(True)
        finder.search_datasheet_mouser.set(False)
        finder.search_datasheet_rs.set(False)
        finder.search_datasheet_generic.set(False)
        
        finder.search_3d_manufacturer.set(True)
        finder.search_3d_ultralibrarian.set(True)
        finder.search_3d_samacsys.set(True)
        finder.search_3d_snapeda.set(True)
        
        # Mock the update_matrix_cell to avoid Excel operations
        finder.update_matrix_cell = MagicMock()
        
        # Capture all comments
        comments = []
        original_add_comment = finder.add_comment
        
        def capture_comment(message):
            comments.append(f"{len(comments)+1:3d}. {message}")
            print(f"   {len(comments):3d}. {message}")
        
        finder.add_comment = capture_comment
        
        print("🔍 Step 1: Testing datasheet search...")
        
        # Test datasheet search directly
        datasheet_result = finder.search_datasheet_enhanced('Murata', 'GCM155R71H104KE02D')
        print(f"   Datasheet result: {datasheet_result}")
        
        if datasheet_result and datasheet_result.get('success'):
            print("   ✅ Datasheet search works")
            
            # Check if PDF parsing worked
            if datasheet_result.get('pdf_parsing'):
                pdf_info = datasheet_result['pdf_parsing']
                print(f"   📖 PDF parsing: {pdf_info.get('success', False)}")
                
                if pdf_info.get('part_validation'):
                    valid = pdf_info['part_validation'].get('valid', False)
                    print(f"   🔍 Part validation: {'Valid' if valid else 'Invalid'}")
                
                if pdf_info.get('package_info'):
                    package = pdf_info['package_info'].get('package')
                    print(f"   📦 Package: {package or 'None found'}")
            
            print("\n🔍 Step 2: Testing STEP file search...")
            
            # Test STEP search directly
            step_result = finder.search_step_enhanced('Murata', 'GCM155R71H104KE02D')
            print(f"   STEP result: {step_result}")
            
            if step_result and step_result.get('success'):
                print("   ✅ STEP search works")
            else:
                print("   ❌ STEP search failed")
                if step_result:
                    print(f"   Error: {step_result.get('message', 'No message')}")
        else:
            print("   ❌ Datasheet search failed")
            if datasheet_result:
                print(f"   Error: {datasheet_result.get('message', 'No message')}")
        
        print("\n🔍 Step 3: Testing full process_matrix_row...")
        
        # Mock dialogs to avoid GUI issues
        with patch('tkinter.messagebox.askyesnocancel', return_value=True):
            with patch('tkinter.simpledialog.askstring', return_value=None):
                
                # Clear comments for full test
                comments.clear()
                
                try:
                    # Run the full process
                    finder.process_matrix_row(0, 'Murata', 'GCM155R71H104KE02D')
                    print("   ✅ Full process completed")
                    
                    print(f"\n📊 FULL PROCESS RESULTS:")
                    print(f"   Total comments: {len(comments)}")
                    
                    # Analyze results
                    datasheet_found = any('found datasheet' in c.lower() for c in comments)
                    step_searched = any('step' in c.lower() and ('searching' in c.lower() or 'found' in c.lower()) for c in comments)
                    
                    print(f"   Datasheet found: {'✅' if datasheet_found else '❌'}")
                    print(f"   STEP search attempted: {'✅' if step_searched else '❌'}")
                    
                    print(f"\n📋 All comments:")
                    for comment in comments:
                        print(f"     {comment}")
                    
                except Exception as e:
                    print(f"   ❌ Full process failed: {e}")
                    print(f"   📍 Error type: {type(e).__name__}")
                    traceback.print_exc()
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_component_finder_workflow()
    if success:
        print(f"\n🎉 TEST COMPLETED")
    else:
        print(f"\n❌ TEST FAILED")
