#!/usr/bin/env python3
"""
Script to help set up VS Code with Mermaid support for flowchart editing
"""

import webbrowser
import subprocess
import sys
import os
from pathlib import Path

def check_vscode_installed():
    """Check if VS Code is installed"""
    try:
        # Try to run 'code --version'
        result = subprocess.run(['code', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ VS Code is installed and accessible via 'code' command")
            return True
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    print("❌ VS Code not found or 'code' command not available")
    return False

def install_vscode_extensions():
    """Install recommended VS Code extensions for Mermaid"""
    extensions = [
        "bierner.markdown-mermaid",  # Markdown Preview Mermaid Support
        "vstirbu.vscode-mermaid-preview",  # Mermaid Preview
        "ms-vscode.vscode-json",  # JSON support
        "yzhang.markdown-all-in-one"  # Enhanced Markdown support
    ]
    
    print("📦 Installing VS Code extensions...")
    
    for ext in extensions:
        try:
            print(f"Installing {ext}...")
            result = subprocess.run(['code', '--install-extension', ext], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print(f"✅ {ext} installed successfully")
            else:
                print(f"⚠️ {ext} installation may have failed: {result.stderr}")
        except subprocess.TimeoutExpired:
            print(f"⏰ {ext} installation timed out")
        except Exception as e:
            print(f"❌ Error installing {ext}: {e}")

def create_vscode_settings():
    """Create VS Code settings for better Mermaid experience"""
    
    # Create .vscode directory if it doesn't exist
    vscode_dir = Path(".vscode")
    vscode_dir.mkdir(exist_ok=True)
    
    # Settings for better Mermaid experience
    settings = {
        "markdown-mermaid.lightModeTheme": "default",
        "markdown-mermaid.darkModeTheme": "dark",
        "markdown.preview.breaks": True,
        "markdown.preview.linkify": True,
        "files.associations": {
            "*.mmd": "mermaid"
        },
        "mermaid-preview.theme": "default"
    }
    
    settings_file = vscode_dir / "settings.json"
    
    import json
    with open(settings_file, 'w') as f:
        json.dump(settings, f, indent=2)
    
    print(f"✅ VS Code settings created: {settings_file}")

def create_launch_script():
    """Create a script to easily open flowchart in VS Code"""
    
    script_content = '''@echo off
echo 🎨 Opening Component Finder Flowchart in VS Code...
code Component_Finder_Flowchart.md
echo ✅ VS Code should open with the flowchart
echo 💡 Use Ctrl+Shift+V to preview the Mermaid diagram
pause
'''
    
    script_file = Path("open_flowchart_vscode.bat")
    with open(script_file, 'w') as f:
        f.write(script_content)
    
    print(f"✅ VS Code launcher created: {script_file}")
    print("💡 Double-click this .bat file to open flowchart in VS Code")

def setup_vscode_mermaid():
    """Main setup function"""
    print("🔧 VS Code Mermaid Setup")
    print("=" * 50)
    
    # Check if VS Code is installed
    if not check_vscode_installed():
        print("\n📥 VS Code Installation Required:")
        print("1. Download VS Code from: https://code.visualstudio.com/")
        print("2. Install VS Code")
        print("3. Make sure 'Add to PATH' is checked during installation")
        print("4. Restart your command prompt/terminal")
        print("5. Run this script again")
        
        # Open VS Code download page
        response = input("\n🌐 Open VS Code download page? (y/n): ")
        if response.lower() == 'y':
            webbrowser.open("https://code.visualstudio.com/download")
        
        return False
    
    # Install extensions
    install_vscode_extensions()
    
    # Create settings
    create_vscode_settings()
    
    # Create launch script
    create_launch_script()
    
    print("\n🎯 Setup Complete! Here's how to use it:")
    print("=" * 50)
    print("1. Double-click 'open_flowchart_vscode.bat' to open in VS Code")
    print("2. In VS Code, press Ctrl+Shift+V to preview Mermaid diagram")
    print("3. Edit the diagram code in the left pane")
    print("4. See live preview in the right pane")
    print("5. Right-click preview → 'Export' to save as PNG/SVG")
    
    print("\n📚 VS Code Mermaid Commands:")
    print("- Ctrl+Shift+V: Open preview")
    print("- Ctrl+K V: Open preview to the side")
    print("- F1 → 'Mermaid: Preview': Open Mermaid preview")
    
    # Test by opening the flowchart
    response = input("\n🚀 Open flowchart in VS Code now? (y/n): ")
    if response.lower() == 'y':
        try:
            subprocess.run(['code', 'Component_Finder_Flowchart.md'])
            print("✅ VS Code opened with flowchart!")
        except Exception as e:
            print(f"❌ Error opening VS Code: {e}")
    
    return True

if __name__ == "__main__":
    setup_vscode_mermaid()
