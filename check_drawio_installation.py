#!/usr/bin/env python3
"""
Script to check if Draw.io Desktop is installed and help with installation issues
"""

import os
import subprocess
import webbrowser
from pathlib import Path
import winreg

def check_drawio_installed():
    """Check if Draw.io Desktop is installed on Windows"""
    print("🔍 Checking for Draw.io Desktop installation...")
    
    # Common installation paths
    common_paths = [
        Path(os.environ.get('LOCALAPPDATA', '')) / 'Programs' / 'drawio' / 'draw.io.exe',
        Path(os.environ.get('PROGRAMFILES', '')) / 'draw.io' / 'draw.io.exe',
        Path(os.environ.get('PROGRAMFILES(X86)', '')) / 'draw.io' / 'draw.io.exe',
        Path(os.environ.get('APPDATA', '')) / 'draw.io' / 'draw.io.exe'
    ]
    
    # Check each path
    for path in common_paths:
        if path.exists():
            print(f"✅ Found Draw.io at: {path}")
            return str(path)
    
    # Check Windows registry
    try:
        print("🔍 Checking Windows registry...")
        
        # Check uninstall registry entries
        registry_paths = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        ]
        
        for reg_path in registry_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path) as key:
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    if "draw.io" in display_name.lower():
                                        install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                                        exe_path = Path(install_location) / "draw.io.exe"
                                        if exe_path.exists():
                                            print(f"✅ Found Draw.io in registry: {exe_path}")
                                            return str(exe_path)
                                except FileNotFoundError:
                                    pass
                            i += 1
                        except OSError:
                            break
            except FileNotFoundError:
                continue
                
    except Exception as e:
        print(f"⚠️ Registry check failed: {e}")
    
    print("❌ Draw.io Desktop not found")
    return None

def check_installer_file():
    """Check if the installer file exists"""
    installer_files = [
        "draw.io-28.1.2-windows-installer.exe",
        "drawio-28.1.2-windows-installer.exe"
    ]
    
    for installer in installer_files:
        if Path(installer).exists():
            print(f"✅ Found installer: {installer}")
            return installer
    
    print("❌ Installer file not found")
    return None

def run_installer_as_admin(installer_path):
    """Try to run the installer with admin privileges"""
    try:
        print(f"🚀 Attempting to run {installer_path} as administrator...")
        
        # Use PowerShell to run as admin
        powershell_cmd = f'Start-Process "{installer_path}" -Verb RunAs -Wait'
        
        result = subprocess.run([
            'powershell', '-Command', powershell_cmd
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Installer completed successfully!")
            return True
        else:
            print(f"⚠️ Installer returned code: {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Installer is taking a while - this is normal for first-time installation")
        print("💡 Please wait for the installer to complete")
        return True
    except Exception as e:
        print(f"❌ Failed to run installer: {e}")
        return False

def launch_drawio():
    """Try to launch Draw.io Desktop"""
    drawio_path = check_drawio_installed()
    
    if drawio_path:
        try:
            print(f"🚀 Launching Draw.io Desktop...")
            subprocess.Popen([drawio_path])
            print("✅ Draw.io Desktop launched!")
            return True
        except Exception as e:
            print(f"❌ Failed to launch Draw.io: {e}")
            return False
    else:
        print("❌ Cannot launch - Draw.io not found")
        return False

def provide_manual_instructions():
    """Provide manual installation instructions"""
    print("\n📋 Manual Installation Instructions:")
    print("=" * 50)
    print("1. Locate the installer file: draw.io-28.1.2-windows-installer.exe")
    print("2. Right-click on the installer")
    print("3. Select 'Run as administrator'")
    print("4. If Windows SmartScreen appears, click 'More info' then 'Run anyway'")
    print("5. Follow the installation wizard")
    print("6. Choose installation location (default is usually fine)")
    print("7. Wait for installation to complete")
    print("8. Look for Draw.io Desktop in Start Menu or Desktop")
    
    print("\n🔧 Alternative Download Sources:")
    print("- Official GitHub: https://github.com/jgraph/drawio-desktop/releases")
    print("- Microsoft Store: Search for 'draw.io'")
    print("- Official website: https://www.drawio.com/")

def main():
    """Main troubleshooting function"""
    print("🔧 Draw.io Desktop Installation Checker")
    print("=" * 50)
    
    # Check if already installed
    drawio_path = check_drawio_installed()
    
    if drawio_path:
        print("🎉 Draw.io Desktop is already installed!")
        
        # Ask if user wants to launch it
        launch = input("\n🚀 Launch Draw.io Desktop now? (y/n): ").lower()
        if launch == 'y':
            if launch_drawio():
                print("\n💡 To import your flowchart:")
                print("1. File → Import From → Text")
                print("2. Select 'Mermaid' format")
                print("3. Paste code from component_finder_flowchart.mmd")
            return
    
    # Check for installer file
    installer = check_installer_file()
    
    if installer:
        print(f"\n📦 Found installer: {installer}")
        
        # Ask if user wants to try installation again
        retry = input("\n🔄 Try running installer as administrator? (y/n): ").lower()
        
        if retry == 'y':
            if run_installer_as_admin(installer):
                print("\n🔍 Checking installation again...")
                drawio_path = check_drawio_installed()
                
                if drawio_path:
                    print("🎉 Installation successful!")
                    launch = input("\n🚀 Launch Draw.io Desktop now? (y/n): ").lower()
                    if launch == 'y':
                        launch_drawio()
                else:
                    print("⚠️ Installation may not have completed properly")
                    provide_manual_instructions()
            else:
                provide_manual_instructions()
        else:
            provide_manual_instructions()
    else:
        print("❌ Installer file not found")
        print("🌐 Opening download page...")
        webbrowser.open("https://github.com/jgraph/drawio-desktop/releases/latest")
        provide_manual_instructions()

if __name__ == "__main__":
    main()
