#!/usr/bin/env python3
"""
Refresh Digikey API token and test with datasheets
"""

import requests
import json
import os
import time

def refresh_digikey_token():
    print("REFRESHING DIGIKEY API TOKEN")
    print("=" * 50)

    # Load your saved credentials
    try:
        with open('digikey_api_credentials.json', 'r') as f:
            creds = json.load(f)
            client_id = creds['client_id']
            client_secret = creds['client_secret']
        print("Loaded saved credentials")
        print(f"   Client ID: {client_id[:20]}...")
    except:
        print("No saved credentials found")
        return False

    # Get new access token
    print("\nGetting new access token...")
    
    token_url = "https://api.digikey.com/v1/oauth2/token"
    
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
    }
    
    data = {
        'client_id': client_id,
        'client_secret': client_secret,
        'grant_type': 'client_credentials'
    }
    
    try:
        response = requests.post(token_url, headers=headers, data=data, timeout=30)
        
        print(f"   Token request status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get('access_token')
            
            if access_token:
                print("   New access token obtained!")
                print(f"   Token: {access_token[:30]}...")

                # Update and save credentials
                creds['access_token'] = access_token

                with open('digikey_api_credentials.json', 'w') as f:
                    json.dump(creds, f, indent=2)
                print("   Updated credentials saved!")

                return access_token
            else:
                print("   No access token in response")
                return False
        else:
            print(f"   Token request failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False

    except Exception as e:
        print(f"   Token request error: {str(e)}")
        return False

def test_with_fresh_token(access_token, client_id):
    """Test API with fresh token - just verify it works, no downloads"""
    print(f"\nTESTING WITH FRESH TOKEN")
    print("=" * 50)

    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-DIGIKEY-Client-Id': client_id
    }

    # Just test that the token works with a simple API call
    print("Testing token with simple API call...")

    successful_test = 0

    try:
        # Just test with a simple search to verify token works
        search_url = "https://api.digikey.com/products/v4/search/keyword"

        search_data = {
            'keywords': 'resistor',
            'recordCount': 1,
            'recordStartPosition': 0,
            'filters': {},
            'sort': {
                'option': 'SortByUnitPrice',
                'direction': 'Ascending'
            },
            'requestedQuantity': 1
        }

        print("Testing token with simple search...")
        response = requests.post(search_url, headers=headers, json=search_data, timeout=30)

        print(f"   API Status: {response.status_code}")

        if response.status_code == 200:
            results = response.json()
            products = results.get('Products', [])

            if products:
                print("   Token test successful - API is working")
                successful_test = 1
            else:
                print("   Token works but no products returned")
        else:
            print(f"   API Error: {response.status_code}")

    except Exception as e:
        print(f"   Error: {str(e)}")

    return successful_test


def create_proper_filename(manufacturer, part_number, description):
    """Create proper filename format: Manufacturer-PartNumber.pdf"""
    # Clean manufacturer
    mfg_clean = clean_text(manufacturer)

    # Clean part number
    part_clean = clean_text(part_number)

    # Format: Manufacturer-PartNumber.pdf
    filename = f"{mfg_clean}-{part_clean}.pdf"

    return filename

def clean_text(text):
    """Clean text for filename"""
    if not text or text == 'N/A':
        return 'Unknown'
    
    import re
    # Replace problematic characters
    text = re.sub(r'[<>:"/\\|?*]', '_', text)
    text = re.sub(r'[^\w\s\-_.]', '', text)
    text = re.sub(r'\s+', '_', text)
    text = re.sub(r'_+', '_', text)
    text = text.strip('_')
    
    return text

def download_datasheet(url, filename):
    """Download datasheet file"""
    try:
        print(f"       Downloading...")
        
        response = requests.get(url, timeout=60)
        
        if response.status_code == 200:
            filepath = os.path.join('datasheets', filename)
            
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            print(f"      Size: {len(response.content):,} bytes")
            return True
        else:
            print(f"      HTTP {response.status_code}")
            return False

    except Exception as e:
        print(f"      Error: {str(e)[:30]}")
        return False

def main():
    print("DIGIKEY TOKEN REFRESH & DATASHEET TEST")
    print("=" * 60)
    
    # Refresh token
    access_token = refresh_digikey_token()
    
    if access_token:
        # Load client_id
        with open('digikey_api_credentials.json', 'r') as f:
            creds = json.load(f)
            client_id = creds['client_id']
        
        # Test with fresh token
        test_result = test_with_fresh_token(access_token, client_id)

        print(f"\n" + "=" * 60)
        print(f" FINAL RESULTS")
        print(f"=" * 60)

        if test_result > 0:
            print(f" SUCCESS! Digikey API token is working")
        else:
            print(f" Token test failed")
    else:
        print(f"\nToken refresh failed")

if __name__ == "__main__":
    main()
