  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 📊 Loading Excel file: E:/Python/web-get-files/Teledyne_Flir_master-footprint_list-tura.xlsx
 15. ✅ Loaded Excel file: 17 rows, 17 columns
 16. 📋 Columns: ['Teledyne Flir  footprint name  to manufacturer cross reference', 'Footprint source  Program', 'Unnamed: 2', 'Physical Description', 'boared name', 'Manufacturer Name ', 'Manufacturer full part number', 'Data Sheet Link on Computer', 'Case Code', '3D Source', '3D file name', 'Created Date', 'Verified Date', 'BODY WIDTH - TOLERANCE', 'BODY-LENGTH-TOLERANCE', 'PAD WIDTH-TOLERANCE', 'HEIGHT MAX']
 17. 🔍 Excel part file search mode enabled!
 18. 📊 Column mapping: {'manufacturer': 'Manufacturer Name ', 'part_number': 'Manufacturer full part number', 'datasheet': None, 'step_source': None, 'step_file': None}
 19. 🔍 Starting to process ALL parts in matrix...
 20. 
📊 Processing Row 7: Murata GCM155R71H104KE02D
 21. 🚀 STARTING process_matrix_row for Murata GCM155R71H104KE02D
 22. 📊 Row 7: Mfg='Murata', Part='GCM155R71H104KE02D'
 23. 📊 Datasheet='', STEP=''
 24. 📄 Using enhanced datasheet finder for Murata GCM155R71H104KE02D...
 25. 🔍 Datasheet needed: True
 26. 🔍 Calling external datasheet finder...
 27. 🔍 External datasheet finder returned: True
 28. ✅ Digikey found datasheet!
 29. ❌ Error processing matrix row: cannot access local variable 'os' where it is not associated with a value
 30. ⏸️ PAUSING after part 1 - waiting for user...
 31. ⏸️ Processing stopped by user after 1 parts
 32. 
🎉 Matrix processing complete!
 33. 📊 Total rows processed: 1
 34. ✅ Parts found: 1
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 📊 Loading Excel file: E:/Python/web-get-files/Teledyne_Flir_master-footprint_list-tura.xlsx
 15. ✅ Loaded Excel file: 17 rows, 17 columns
 16. 📋 Columns: ['Teledyne Flir  footprint name  to manufacturer cross reference', 'Footprint source  Program', 'Unnamed: 2', 'Physical Description', 'boared name', 'Manufacturer Name ', 'Manufacturer full part number', 'Data Sheet Link on Computer', 'Case Code', '3D Source', '3D file name', 'Created Date', 'Verified Date', 'BODY WIDTH - TOLERANCE', 'BODY-LENGTH-TOLERANCE', 'PAD WIDTH-TOLERANCE', 'HEIGHT MAX']
 17. 🔍 Excel part file search mode enabled!
 18. 📊 Column mapping: {'manufacturer': 'Manufacturer Name ', 'part_number': 'Manufacturer full part number', 'datasheet': None, 'step_source': None, 'step_file': None}
 19. 🔍 Starting to process ALL parts in matrix...
 20. 
📊 Processing Row 7: Murata GCM155R71H104KE02D
 21. 🚀 STARTING process_matrix_row for Murata GCM155R71H104KE02D
 22. 📊 Row 7: Mfg='Murata', Part='GCM155R71H104KE02D'
 23. 📊 Datasheet='', STEP=''
 24. 📄 Using enhanced datasheet finder for Murata GCM155R71H104KE02D...
 25. 🔍 Datasheet needed: True
 26. 🔍 Calling external datasheet finder...
 27. 🔍 External datasheet finder returned: True
 28. ✅ Digikey found datasheet!
 29. 📄 Downloaded: Murata_GCM155R71H104KE02D_datasheet.pdf
 30. 💾 Updated matrix with new datasheet location
 31. ❌ Part number GCM155R71H104KE02D not found in datasheet
 32. 🔍 About to show part validation dialog...
 33. 🔍 Showing part validation dialog with View PDF option...
 34. 🔍 Dialog returned: view_pdf
 35. 📖 Opening PDF: datasheets/Murata_GCM155R71H104KE02D_datasheet.pdf
 36. ❌ Could not open PDF: [WinError 2] The system cannot find the file specified: 'datasheets/Murata_GCM155R71H104KE02D_datasheet.pdf'
 37. 🔍 3D Search Status: Mfg=True, Ultra=True, Samacsys=True, SnapEDA=True
 38. 🔧 STEP file missing for Murata GCM155R71H104KE02D - using enhanced 3D finder...
 39. 🚀 Using enhanced 3D finder for Murata GCM155R71H104KE02D
 40. ❌ Enhanced 3D finder failed: No STEP file found for Murata GCM155R71H104KE02D
 41. ❌ Enhanced 3D finder failed - no STEP file found
 42. 🎉 Successfully found and downloaded 1 missing files!
 43. ⏸️ PAUSING after part 1 - waiting for user...
 44. ⏸️ Processing stopped by user after 1 parts
 45. 
🎉 Matrix processing complete!
 46. 📊 Total rows processed: 1
 47. ✅ Parts found: 1
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 📊 Loading Excel file: E:/Python/web-get-files/Teledyne_Flir_master-footprint_list-tura.xlsx
 15. ✅ Loaded Excel file: 17 rows, 17 columns
 16. 📋 Columns: ['Teledyne Flir  footprint name  to manufacturer cross reference', 'Footprint source  Program', 'Unnamed: 2', 'Physical Description', 'boared name', 'Manufacturer Name ', 'Manufacturer full part number', 'Data Sheet Link on Computer', 'Case Code', '3D Source', '3D file name', 'Created Date', 'Verified Date', 'BODY WIDTH - TOLERANCE', 'BODY-LENGTH-TOLERANCE', 'PAD WIDTH-TOLERANCE', 'HEIGHT MAX']
 17. 🔍 Excel part file search mode enabled!
 18. 📊 Column mapping: {'manufacturer': 'Manufacturer Name ', 'part_number': 'Manufacturer full part number', 'datasheet': None, 'step_source': None, 'step_file': None}
 19. 🔍 Starting to process ALL parts in matrix...
 20. 
📊 Processing Row 7: Murata GCM155R71H104KE02D
 21. 🚀 STARTING process_matrix_row for Murata GCM155R71H104KE02D
 22. 📊 Row 7: Mfg='Murata', Part='GCM155R71H104KE02D'
 23. 📊 Datasheet='', STEP=''
 24. 📄 Using enhanced datasheet finder for Murata GCM155R71H104KE02D...
 25. 🔍 Datasheet needed: True
 26. 🔍 Calling external datasheet finder...
 27. 🔍 External datasheet finder returned: True
 28. ✅ Digikey found datasheet!
 29. 📄 Downloaded: Murata_GCM155R71H104KE02D_datasheet.pdf
 30. 💾 Updated matrix with new datasheet location
 31. ❌ Part number GCM155R71H104KE02D not found in datasheet
 32. 🔍 About to show part validation dialog...
 33. 🔍 Showing part validation dialog with View PDF option...
 34. 🔍 Dialog returned: view_pdf
 35. 📖 Opening PDF: E:\Python\web-get-files\datasheets\Murata_GCM155R71H104KE02D_datasheet.pdf
 36. ✅ User chose to continue after viewing PDF
 37. 🔍 3D Search Status: Mfg=True, Ultra=True, Samacsys=True, SnapEDA=True
 38. 🔧 STEP file missing for Murata GCM155R71H104KE02D - using enhanced 3D finder...
 39. 🚀 Using enhanced 3D finder for Murata GCM155R71H104KE02D
 40. ❌ Enhanced 3D finder failed: No STEP file found for Murata GCM155R71H104KE02D
 41. ❌ Enhanced 3D finder failed - no STEP file found
 42. 🎉 Successfully found and downloaded 1 missing files!
 43. ⏸️ PAUSING after part 1 - waiting for user...
 44. ⏸️ Processing stopped by user after 1 parts
 45. 
🎉 Matrix processing complete!
 46. 📊 Total rows processed: 1
 47. ✅ Parts found: 1
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for texas instruments lm358n
 15. 📊 Checking distributors first for part lm358n...
 16. 🔍 Searching Digi-Key website for lm358n...
 17.    Opening: https://www.digikey.com/en/products/result?keywords=lm358n
 18. ✅ Found lm358n on Digi-Key
 19. ✅ Found texas instruments manufacturer on Digi-Key
 20.    Part not found on Digi-Key
 21. 🔍 Searching Mouser website for lm358n...
 22.    Opening: https://www.mouser.com/c/?q=lm358n
 23.    Part not found on Mouser
 24. 🌐 Distributors didn't find part, trying manufacturer website...
 25. 🆕 texas instruments not in knowledge base
 26. 🔍 Searching for texas instruments website...
 27. ✅ Found in CSV: https://www.ti.com
 28. 🌐 Using website: https://www.ti.com
 29. 🔍 Searching https://www.ti.com for part lm358n
 30.    Getting homepage to find search form...
 31.    No search form found, trying common search URLs...
 32.    Trying: https://www.ti.com/search?q=lm358n
 33.    Trying: https://www.ti.com/search?query=lm358n
 34.    Trying: https://www.ti.com/search?keyword=lm358n
 35.    Trying: https://www.ti.com/search?term=lm358n
 36.    Trying: https://www.ti.com/search/lm358n
 37.    Trying: https://www.ti.com/products/search?q=lm358n
 38.    Trying: https://www.ti.com/catalog/search?q=lm358n
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for texas instruments lm358n
 15. 📊 Checking distributors first for part lm358n...
 16. 🔍 Searching Digi-Key website for lm358n...
 17.    Opening: https://www.digikey.com/en/products/result?keywords=lm358n
 18. ✅ Found lm358n on Digi-Key
 19. ✅ Found texas instruments manufacturer on Digi-Key
 20.    Part not found on Digi-Key
 21. 🔍 Searching Mouser website for lm358n...
 22.    Opening: https://www.mouser.com/c/?q=lm358n
 23.    Part not found on Mouser
 24. 🌐 Distributors didn't find part, trying manufacturer website...
 25. 🆕 texas instruments not in knowledge base
 26. 🔍 Searching for texas instruments website...
 27. ✅ Found in CSV: https://www.ti.com
 28. 🌐 Using website: https://www.ti.com
 29. 🔍 Searching https://www.ti.com for part lm358n
 30.    Getting homepage to find search form...
 31.    No search form found, trying common search URLs...
 32.    Trying: https://www.ti.com/search?q=lm358n
 33.    Trying: https://www.ti.com/search?query=lm358n
 34.    Trying: https://www.ti.com/search?keyword=lm358n
 35.    Trying: https://www.ti.com/search?term=lm358n
 36.    Trying: https://www.ti.com/search/lm358n
 37.    Trying: https://www.ti.com/products/search?q=lm358n
 38.    Trying: https://www.ti.com/catalog/search?q=lm358n
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for texas instruments lm358n
 15. 🔍 Using enhanced datasheet finder for texas instruments lm358n...
 16. ✅ External datasheet finder returned: True
 17. ✅ Verified manufacturer: texas instruments
 18. 🌐 Distributors didn't find part, trying manufacturer website...
 19. 🆕 texas instruments not in knowledge base
 20. 🔍 Searching for texas instruments website...
 21. ✅ Found in CSV: https://www.ti.com
 22. 🌐 Using website: https://www.ti.com
 23. 🔍 Searching https://www.ti.com for part lm358n
 24.    Getting homepage to find search form...
 25.    No search form found, trying common search URLs...
 26.    Trying: https://www.ti.com/search?q=lm358n
 27.    Trying: https://www.ti.com/search?query=lm358n
 28.    Trying: https://www.ti.com/search?keyword=lm358n
 29.    Trying: https://www.ti.com/search?term=lm358n
 30.    Trying: https://www.ti.com/search/lm358n
 31.    Trying: https://www.ti.com/products/search?q=lm358n
 32.    Trying: https://www.ti.com/catalog/search?q=lm358n
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for texas instruments lm358n
 15. 🔍 Starting search for texas instruments lm358n
 16. 🚀 Using enhanced 3D finder for texas instruments lm358n
 17. 🌐 Searching UltraLibrarian.com for lm358n...
 18. ✅ 3D file found on UltraLibrarian.com
 19. ✅ Found STEP file: Found via UltraLibrarian
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🔍 Starting search for Texas Instruments LM358N
 15. 📊 Checking distributors first for part LM358N...
 16. 🔍 Searching Digi-Key website for LM358N...
 17.    Opening: https://www.digikey.com/en/products/result?keywords=LM358N
 18. ✅ Found LM358N on Digi-Key
 19. ✅ Found Texas Instruments manufacturer on Digi-Key
 20.    Part found but no datasheet link on Digi-Key
 21. 🔍 Searching Mouser website for LM358N...
 22.    Opening: https://www.mouser.com/c/?q=LM358N
 23.    Part not found on Mouser
 24. 🔍 Searching RS Components website for LM358N...
 25.    Opening: https://uk.rs-online.com/web/c/?searchTerm=LM358N
 26. ✅ Found LM358N on RS Components
 27. ✅ Found Texas Instruments manufacturer on RS Components
 28.    Part found but no datasheet link on RS Components
 29. 🌐 Distributors didn't find part, trying manufacturer website...
 30. 🆕 Texas Instruments not in knowledge base
 31. 🔍 Searching for Texas Instruments website...
 32. ✅ Found in CSV: https://www.ti.com
 33. 🌐 Using website: https://www.ti.com
 34. 🔍 Searching https://www.ti.com for part LM358N
 35.    Getting homepage to find search form...
 36.    No search form found, trying common search URLs...
 37.    Trying: https://www.ti.com/search?q=LM358N
 38.    Trying: https://www.ti.com/search?query=LM358N
 39.    Trying: https://www.ti.com/search?keyword=LM358N
 40.    Trying: https://www.ti.com/search?term=LM358N
 41.    Trying: https://www.ti.com/search/LM358N
 42.    Trying: https://www.ti.com/products/search?q=LM358N
 43.    Trying: https://www.ti.com/catalog/search?q=LM358N
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. 🗑️ Results cleared
 15. 📋 ALL POSSIBLE MESSAGES DEMO
 16. ============================================================
 17. 
 18. 🔍 SEARCH START MESSAGES:
 19. 🔍 Starting search for texas instruments lm358n
 20. 📊 Checking distributors first for part lm358n...
 21. 🔍 Searching Digi-Key website for lm358n...
 22.    Opening: https://www.digikey.com/en/products/result?keywords=lm358n
 23. 
 24. ✅ SUCCESS MESSAGES:
 25. ✅ Found lm358n on Digi-Key
 26. ✅ Found texas instruments manufacturer on Digi-Key
 27. 📄 Found datasheet: https://www.ti.com/lit/gpn/lm358...
 28. 📥 Downloaded: Texas_Instruments_LM358N_datasheet.pdf
 29. ✅ Distributor verified: Texas Instruments → https://www.ti.com
 30. 
 31. ⚠️ WARNING MESSAGES:
 32. ⚠️ texas instruments not explicitly found, but part exists on Digi-Key
 33. ⚠️ Rate limited by Digi-Key
 34. ⚠️ Part found but no datasheet link on Digi-Key
 35. ⚠️ WARNING: This appears to be an incomplete part number
 36. 💡 Complete part numbers usually have suffixes like -PU, -AU, -ND, etc.
 37. 
 38. ❌ ERROR MESSAGES:
 39.    Part not found on Digi-Key
 40. ❌ HTTP 403
 41. ❌ Digi-Key search failed: Connection timeout
 42. ❌ Enhanced datasheet finder failed
 43. ❌ External datasheet finder error: Module not found
 44. 
 45. 🔍 PROGRESS MESSAGES:
 46. 🔍 Searching Mouser website for lm358n...
 47. 🔍 Searching RS Components website for lm358n...
 48. 🌐 Distributors didn't find part, trying manufacturer website...
 49. 🆕 texas instruments not in knowledge base
 50. 🌐 Using website: https://www.ti.com
 51. 
 52. 🎯 STEP FILE MESSAGES:
 53. 🌐 Searching UltraLibrarian.com for lm358n...
 54. ✅ 3D file found on UltraLibrarian.com
 55. 🌐 Searching SamacSys.com for lm358n...
 56. ❌ 3D file not found on SamacSys.com
 57. 🌐 Searching SnapEDA.com for lm358n...
 58. ❌ SnapEDA search error: Timeout
 59. ⏭️ All 3D model searches disabled in options - skipping
 60. 
 61. 🎉 COMPLETION MESSAGES:
 62. ==================================================
 63. 🎉 SEARCH COMPLETE
 64. ==================================================
 65. Manufacturer: Texas Instruments
 66. Part Number: LM358N
 67. Package Type: SOIC
 68. 
 69. ✅ Datasheet: Texas_Instruments_LM358N_datasheet.pdf
 70. ✅ 3D Model: Texas_Instruments_LM358N.step
 71. 📦 Package: SOIC
 72. 
 73. 📊 SUMMARY:
 74. ✅ Success - Found datasheet and 3D model
 75. ⚠️ Partial success - Found datasheet only
 76. ❌ Failed - No files found
 77. 
 78. ============================================================
 79. 📋 END OF DEMO - These are all possible message types
 80. ============================================================
  1. 🚀 Component Finder GUI Started
  2. 📁 Working directory: E:\Python\web-get-files
  3. 📁 Created directories: datasheets/ and 3d/
  4. 📚 Loaded knowledge base
  5. 📋 Loaded 10 manufacturer websites from CSV
  6. 📚 No existing download patterns found - will learn new ones
  7. Ready to search for components!
  8. 💡 Use '📊 Load Excel File' for matrix-based search!
  9. 📚 Help files available in: help_files
 10. 🔧 Testing colored emoji image system...
 11. Test emojis: 🚀 ✅ ❌ ⚠️ 🔍 📥 ℹ️ 🎨 📊 🧠
 12. 👆 These should now be colored emoji images!
 13. ==================================================
 14. ❌ Missing package: pandas
