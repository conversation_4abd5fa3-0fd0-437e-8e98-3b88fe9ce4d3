#!/usr/bin/env python3
"""
Script to download and set up Draw.io Desktop for flowchart editing
"""

import webbrowser
import requests
import json
import platform
import subprocess
import os
from pathlib import Path

def get_latest_drawio_release():
    """Get the latest Draw.io Desktop release info from GitHub"""
    try:
        print("🔍 Checking for latest Draw.io Desktop version...")
        
        # GitHub API to get latest release
        api_url = "https://api.github.com/repos/jgraph/drawio-desktop/releases/latest"
        
        response = requests.get(api_url, timeout=10)
        response.raise_for_status()
        
        release_data = response.json()
        version = release_data['tag_name']
        assets = release_data['assets']
        
        print(f"✅ Latest version: {version}")
        
        return version, assets
        
    except Exception as e:
        print(f"❌ Error getting release info: {e}")
        return None, None

def get_download_url_for_platform(assets):
    """Get the appropriate download URL for the current platform"""
    system = platform.system().lower()
    architecture = platform.machine().lower()
    
    print(f"🖥️ Detected system: {system} ({architecture})")
    
    # Define platform-specific file patterns
    platform_patterns = {
        'windows': {
            'x86_64': ['.exe', 'win32', 'x64'],
            'amd64': ['.exe', 'win32', 'x64'],
            'x86': ['.exe', 'win32', 'ia32']
        },
        'darwin': {  # macOS
            'x86_64': ['.dmg', 'mac', 'x64'],
            'arm64': ['.dmg', 'mac', 'arm64']
        },
        'linux': {
            'x86_64': ['.AppImage', 'linux', 'x86_64'],
            'amd64': ['.AppImage', 'linux', 'x86_64']
        }
    }
    
    if system not in platform_patterns:
        print(f"❌ Unsupported platform: {system}")
        return None, None
    
    # Find matching asset
    for asset in assets:
        name = asset['name'].lower()
        download_url = asset['browser_download_url']
        
        # Check if this asset matches our platform
        if system in platform_patterns:
            arch_patterns = platform_patterns[system].get(architecture, platform_patterns[system].get('x86_64', []))
            
            if any(pattern in name for pattern in arch_patterns):
                print(f"✅ Found matching download: {asset['name']}")
                return download_url, asset['name']
    
    # Fallback: show all available downloads
    print("⚠️ No exact match found. Available downloads:")
    for asset in assets:
        print(f"   - {asset['name']}")
    
    return None, None

def download_file(url, filename):
    """Download a file with progress indication"""
    try:
        print(f"📥 Downloading {filename}...")
        print(f"🔗 URL: {url}")
        
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r📊 Progress: {percent:.1f}% ({downloaded:,} / {total_size:,} bytes)", end='')
        
        print(f"\n✅ Download complete: {filename}")
        return True
        
    except Exception as e:
        print(f"\n❌ Download failed: {e}")
        return False

def setup_drawio_for_mermaid():
    """Create instructions and files for using Draw.io with Mermaid"""
    
    # Create a sample Mermaid file to import
    sample_mermaid = Path("sample_mermaid_for_drawio.txt")
    
    with open(sample_mermaid, 'w', encoding='utf-8') as f:
        f.write("""Instructions for importing Mermaid diagrams into Draw.io:

1. Open Draw.io Desktop
2. Click "Create New Diagram"
3. In the template gallery, look for "Advanced" section
4. Select "Mermaid" or use "Insert" → "Advanced" → "Mermaid"
5. Paste your Mermaid code from component_finder_flowchart.mmd

Alternative method:
1. File → Import From → Text
2. Select "Mermaid" as format
3. Paste your diagram code

Your Component Finder flowchart code is in:
- Component_Finder_Flowchart.md (full file)
- component_finder_flowchart.mmd (clean code only)

Draw.io Features:
- Visual editing with drag & drop
- Export to PDF, PNG, SVG, VSDX
- Professional styling options
- Offline editing
- No account required
""")
    
    print(f"✅ Created setup guide: {sample_mermaid}")

def main():
    """Main function to download and set up Draw.io Desktop"""
    print("🎨 Draw.io Desktop Downloader")
    print("=" * 50)
    
    # Get latest release info
    version, assets = get_latest_drawio_release()
    
    if not version or not assets:
        print("❌ Could not get release information")
        print("🌐 Opening manual download page...")
        webbrowser.open("https://github.com/jgraph/drawio-desktop/releases/latest")
        return
    
    # Get download URL for current platform
    download_url, filename = get_download_url_for_platform(assets)
    
    if not download_url:
        print("❌ Could not find suitable download for your platform")
        print("🌐 Opening releases page for manual download...")
        webbrowser.open("https://github.com/jgraph/drawio-desktop/releases/latest")
        return
    
    # Ask user if they want to download
    print(f"\n📦 Ready to download: {filename}")
    print(f"📏 This will download Draw.io Desktop {version}")
    
    response = input("\n💾 Download now? (y/n): ").lower()
    
    if response != 'y':
        print("🌐 Opening download page for manual download...")
        webbrowser.open("https://github.com/jgraph/drawio-desktop/releases/latest")
        return
    
    # Download the file
    if download_file(download_url, filename):
        print(f"\n🎉 Download successful!")
        print(f"📁 File saved as: {filename}")
        
        # Create setup instructions
        setup_drawio_for_mermaid()
        
        print("\n🚀 Next Steps:")
        print("=" * 30)
        print(f"1. Run the downloaded file: {filename}")
        print("2. Install Draw.io Desktop")
        print("3. Open Draw.io Desktop")
        print("4. Import your Mermaid diagram:")
        print("   - File → Import From → Text")
        print("   - Select 'Mermaid' format")
        print("   - Paste code from component_finder_flowchart.mmd")
        print("5. Edit visually and export as PDF/PNG")
        
        # Ask if user wants to run the installer
        if platform.system().lower() == 'windows' and filename.endswith('.exe'):
            run_installer = input(f"\n🔧 Run installer now? (y/n): ").lower()
            if run_installer == 'y':
                try:
                    print("🚀 Starting installer...")
                    subprocess.Popen([filename])
                    print("✅ Installer started!")
                except Exception as e:
                    print(f"❌ Could not start installer: {e}")
                    print(f"💡 Please run {filename} manually")
    
    else:
        print("❌ Download failed")
        print("🌐 Opening manual download page...")
        webbrowser.open("https://github.com/jgraph/drawio-desktop/releases/latest")

if __name__ == "__main__":
    main()
