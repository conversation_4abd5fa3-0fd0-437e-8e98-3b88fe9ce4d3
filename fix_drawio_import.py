#!/usr/bin/env python3
"""
Script to help properly import Mermaid flowchart into Draw.io Desktop
"""

import webbrowser
from pathlib import Path

def create_drawio_compatible_file():
    """Create a Draw.io compatible file format"""
    
    # Read the mermaid code
    mermaid_file = Path("component_finder_flowchart.mmd")
    
    if not mermaid_file.exists():
        print("❌ component_finder_flowchart.mmd not found!")
        return None
    
    with open(mermaid_file, 'r', encoding='utf-8') as f:
        mermaid_code = f.read().strip()
    
    # Create a Draw.io XML file with embedded Mermaid
    drawio_xml = f'''<mxfile host="Electron" modified="2024-01-01T00:00:00.000Z" agent="5.0" version="22.1.11" etag="abc123">
  <diagram name="Component Finder Flowchart" id="flowchart">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="mermaid-1" value="{mermaid_code}" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontFamily=monospace;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="740" height="1080" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>'''
    
    # Save as .drawio file
    drawio_file = Path("Component_Finder_Flowchart.drawio")
    with open(drawio_file, 'w', encoding='utf-8') as f:
        f.write(drawio_xml)
    
    print(f"✅ Created Draw.io file: {drawio_file}")
    return str(drawio_file.absolute())

def provide_correct_import_steps():
    """Provide the correct steps to import Mermaid into Draw.io"""
    print("\n🎯 CORRECT Way to Import Mermaid into Draw.io:")
    print("=" * 60)
    print("❌ The previous method didn't work because Draw.io's Mermaid import")
    print("   is limited. Here are better approaches:")
    
    print("\n🔧 METHOD 1 - Use Mermaid Live Editor (Recommended):")
    print("1. Go to https://mermaid.live/")
    print("2. Paste your Mermaid code")
    print("3. Click 'Actions' → 'Export SVG'")
    print("4. In Draw.io: File → Import → select the SVG file")
    
    print("\n🔧 METHOD 2 - Manual Recreation in Draw.io:")
    print("1. Open Draw.io Desktop")
    print("2. Use the flowchart shapes from the left panel")
    print("3. Manually recreate the flowchart using drag & drop")
    print("4. This gives you full control over styling")
    
    print("\n🔧 METHOD 3 - Use Online Draw.io with Mermaid Plugin:")
    print("1. Go to https://app.diagrams.net/")
    print("2. Create new diagram")
    print("3. Go to Extras → Plugins → Add → Mermaid")
    print("4. Insert → Advanced → Mermaid")
    print("5. Paste your code in the Mermaid dialog")

def open_mermaid_live_with_code():
    """Open Mermaid Live Editor with instructions"""
    print("\n🚀 Opening Mermaid Live Editor...")
    print("📋 Your flowchart code is copied to clipboard")
    
    # Copy to clipboard
    try:
        import pyperclip
        mermaid_file = Path("component_finder_flowchart.mmd")
        if mermaid_file.exists():
            with open(mermaid_file, 'r', encoding='utf-8') as f:
                mermaid_code = f.read().strip()
            pyperclip.copy(mermaid_code)
            print("✅ Code copied to clipboard!")
        else:
            print("⚠️ Mermaid file not found")
    except Exception as e:
        print(f"⚠️ Could not copy to clipboard: {e}")
    
    # Open Mermaid Live Editor
    webbrowser.open("https://mermaid.live/")
    
    print("\n🎯 In Mermaid Live Editor:")
    print("1. Clear the default diagram")
    print("2. Paste your code (Ctrl+V)")
    print("3. Click 'Actions' → 'Export SVG'")
    print("4. Save the SVG file")
    print("5. Import the SVG into Draw.io")

def create_simple_instructions_file():
    """Create a simple text file with all the steps"""
    instructions = """DRAW.IO FLOWCHART IMPORT - STEP BY STEP GUIDE
==============================================

PROBLEM: Draw.io's direct Mermaid import doesn't work well.

SOLUTION: Use Mermaid Live Editor to convert to SVG, then import SVG.

STEP-BY-STEP PROCESS:
====================

1. CONVERT MERMAID TO SVG:
   - Go to: https://mermaid.live/
   - Clear the default diagram
   - Paste your flowchart code (from component_finder_flowchart.mmd)
   - The diagram will render automatically
   - Click "Actions" → "Export SVG"
   - Save as "Component_Finder_Flowchart.svg"

2. IMPORT SVG INTO DRAW.IO:
   - Open Draw.io Desktop
   - File → Import
   - Select your saved SVG file
   - The flowchart will import as a visual diagram

3. EDIT IN DRAW.IO:
   - Now you can edit colors, shapes, text
   - Drag and drop to rearrange
   - Add new elements
   - Export as PDF: File → Export as → PDF

ALTERNATIVE - ONLINE DRAW.IO WITH MERMAID:
==========================================
   - Go to: https://app.diagrams.net/
   - Extras → Plugins → Add → Mermaid
   - Insert → Advanced → Mermaid
   - Paste your code directly

YOUR FILES:
===========
- Mermaid code: component_finder_flowchart.mmd
- Full documentation: Component_Finder_Flowchart.md
- This guide: drawio_import_guide.txt
"""
    
    guide_file = Path("drawio_import_guide.txt")
    with open(guide_file, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"✅ Created step-by-step guide: {guide_file}")
    return str(guide_file.absolute())

def main():
    """Main function to fix Draw.io import"""
    print("🔧 Draw.io Mermaid Import Fixer")
    print("=" * 50)
    
    print("❌ Direct Mermaid import into Draw.io often doesn't work properly")
    print("✅ Let's use the correct method...")
    
    # Create instruction guide
    guide_file = create_simple_instructions_file()
    
    # Provide the correct steps
    provide_correct_import_steps()
    
    # Ask user which method they prefer
    print("\n🎯 Which method would you like to try?")
    print("1. 🌐 Open Mermaid Live Editor (convert to SVG)")
    print("2. 🌐 Open online Draw.io with Mermaid plugin")
    print("3. 📄 Just show me the guide file")
    
    choice = input("\nEnter choice (1/2/3): ").strip()
    
    if choice == "1":
        open_mermaid_live_with_code()
    elif choice == "2":
        print("🌐 Opening online Draw.io...")
        webbrowser.open("https://app.diagrams.net/")
        print("💡 Remember to add the Mermaid plugin: Extras → Plugins → Add → Mermaid")
    else:
        print(f"📄 Step-by-step guide saved to: {guide_file}")
        print("💡 Follow the instructions in the guide file")

if __name__ == "__main__":
    main()
