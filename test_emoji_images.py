#!/usr/bin/env python3
"""
Test script to verify emoji images are working correctly
"""

import tkinter as tk
from tkinter import scrolledtext
from pathlib import Path

def test_emoji_images():
    """Test if emoji images display correctly"""
    
    root = tk.Tk()
    root.title("Emoji Image Test")
    root.geometry("600x400")
    
    # Check if emoji images exist
    emoji_dir = Path("emoji_images")
    
    if not emoji_dir.exists():
        tk.Label(root, text="❌ emoji_images directory not found!", fg='red', font=('Arial', 12, 'bold')).pack(pady=20)
        root.mainloop()
        return
    
    # List emoji files
    emoji_files = list(emoji_dir.glob("*.png"))
    
    if not emoji_files:
        tk.Label(root, text="❌ No emoji PNG files found!", fg='red', font=('Arial', 12, 'bold')).pack(pady=20)
        root.mainloop()
        return
    
    tk.Label(root, text=f"✅ Found {len(emoji_files)} emoji images", fg='green', font=('Arial', 12, 'bold')).pack(pady=10)
    
    # Test loading images
    text_widget = scrolledtext.ScrolledText(root, height=15, width=70)
    text_widget.pack(pady=10, padx=10, fill='both', expand=True)
    
    # Test each emoji image
    try:
        from PIL import Image, ImageTk
        
        text_widget.insert(tk.END, "Testing emoji images:\n\n")
        
        emoji_refs = []  # Keep references to prevent garbage collection
        
        for i, file_path in enumerate(emoji_files[:10]):  # Test first 10
            try:
                # Load image
                image = Image.open(file_path)
                image = image.resize((20, 20), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(image)
                emoji_refs.append(photo)
                
                # Insert image
                text_widget.insert(tk.END, f"{file_path.stem}: ")
                text_widget.image_create(tk.END, image=photo)
                text_widget.insert(tk.END, " <- Should be colored!\n")
                
            except Exception as e:
                text_widget.insert(tk.END, f"❌ Failed to load {file_path.name}: {e}\n")
        
        text_widget.insert(tk.END, "\n👆 If you see colored emoji images above, the system works!")
        text_widget.insert(tk.END, "\nIf you only see text, there's a problem with image loading.")
        
    except ImportError:
        text_widget.insert(tk.END, "❌ PIL/Pillow not available - cannot load images!")
    except Exception as e:
        text_widget.insert(tk.END, f"❌ Error testing images: {e}")
    
    # Result buttons
    def report_working():
        print("✅ USER REPORTS: Emoji images ARE working and colored!")
        root.quit()
    
    def report_not_working():
        print("❌ USER REPORTS: Emoji images are NOT working")
        root.quit()
    
    button_frame = tk.Frame(root)
    button_frame.pack(pady=10)
    
    working_btn = tk.Button(button_frame, text="✅ I see COLORED emoji images", 
                           command=report_working, bg='green', fg='white', font=('Arial', 10, 'bold'))
    working_btn.pack(side=tk.LEFT, padx=10)
    
    not_working_btn = tk.Button(button_frame, text="❌ No colored images", 
                               command=report_not_working, bg='red', fg='white', font=('Arial', 10, 'bold'))
    not_working_btn.pack(side=tk.LEFT, padx=10)
    
    print("🔍 Emoji image test window opened")
    print("👀 Please check if you see colored emoji images and click the appropriate button")
    
    root.mainloop()

if __name__ == "__main__":
    test_emoji_images()
