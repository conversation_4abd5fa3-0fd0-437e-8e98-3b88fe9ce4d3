#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to automatically open the Component Finder flowchart in Mermaid Live Editor
"""

import webbrowser
import urllib.parse
from pathlib import Path

def open_mermaid_live():
    """Open the flowchart in Mermaid Live Editor with the diagram pre-loaded"""
    
    # Read the mermaid diagram code
    flowchart_file = Path("Component_Finder_Flowchart.md")
    
    if not flowchart_file.exists():
        print("❌ Component_Finder_Flowchart.md not found!")
        print("Please make sure the file exists in the current directory.")
        return
    
    # Extract mermaid code from the markdown file
    with open(flowchart_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the mermaid code block
    start_marker = "```mermaid"
    end_marker = "```"
    
    start_idx = content.find(start_marker)
    if start_idx == -1:
        print("❌ Mermaid code block not found in the file!")
        return
    
    start_idx += len(start_marker)
    end_idx = content.find(end_marker, start_idx)
    
    if end_idx == -1:
        print("❌ End of mermaid code block not found!")
        return
    
    mermaid_code = content[start_idx:end_idx].strip()
    
    # Create the Mermaid Live Editor URL with the diagram pre-loaded
    base_url = "https://mermaid.live/edit"
    
    # Create a simple diagram state for the URL
    # Mermaid Live Editor uses a specific format for sharing diagrams
    diagram_data = {
        "code": mermaid_code,
        "mermaid": {"theme": "default"},
        "autoSync": True,
        "updateDiagram": True
    }
    
    # For simplicity, we'll open Mermaid Live and the user can paste the code
    print("🚀 Opening Mermaid Live Editor...")
    print("📋 The flowchart code will be copied to clipboard (if possible)")
    
    # Try to copy to clipboard
    try:
        import pyperclip
        pyperclip.copy(mermaid_code)
        print("✅ Flowchart code copied to clipboard!")
        print("📝 Just paste (Ctrl+V) in the Mermaid Live Editor")
    except ImportError:
        print("⚠️ pyperclip not available - you'll need to copy manually")
        print("📄 The code is in Component_Finder_Flowchart.md")
    
    # Open Mermaid Live Editor
    webbrowser.open("https://mermaid.live/")
    
    print("\n🎯 Instructions:")
    print("1. Mermaid Live Editor should open in your browser")
    print("2. Clear the default diagram")
    print("3. Paste the flowchart code (Ctrl+V)")
    print("4. The diagram will render automatically")
    print("5. Use 'Actions' menu to export as PDF, PNG, or SVG")
    
    # Also save a clean mermaid file for easy copying
    mermaid_file = Path("component_finder_flowchart.mmd")
    with open(mermaid_file, 'w', encoding='utf-8') as f:
        f.write(mermaid_code)
    
    print(f"\n📁 Clean mermaid code saved to: {mermaid_file}")
    print("💡 You can also open this .mmd file directly in VS Code with Mermaid extension")

def install_pyperclip():
    """Install pyperclip for clipboard functionality"""
    try:
        import subprocess
        import sys
        
        print("📦 Installing pyperclip for clipboard support...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyperclip"])
        print("✅ pyperclip installed successfully!")
        return True
    except Exception as e:
        print(f"❌ Failed to install pyperclip: {e}")
        return False

if __name__ == "__main__":
    print("🎨 Component Finder Flowchart Opener")
    print("=" * 50)
    
    # Try to install pyperclip if not available
    try:
        import pyperclip
    except ImportError:
        print("📦 pyperclip not found - attempting to install...")
        if install_pyperclip():
            import pyperclip
    
    open_mermaid_live()
