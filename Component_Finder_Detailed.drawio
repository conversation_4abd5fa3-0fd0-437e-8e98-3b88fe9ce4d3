<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T12:00:00.000Z" agent="5.0" version="22.1.11" etag="abc123" type="device">
  <diagram name="Component Finder - Step by Step" id="flowchart">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="2400" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- PROGRAM START -->
        <mxCell id="start" value="__init__()&#10;Program Start" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="1">
          <mxGeometry x="700" y="20" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- INITIALIZATION -->
        <mxCell id="setupEmoji" value="_setup_emoji_images()&#10;Download 25 emoji PNGs" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="1">
          <mxGeometry x="680" y="120" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="createDirs" value="Create Directories:&#10;datasheets/, 3d/, help_files/" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="1">
          <mxGeometry x="680" y="220" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="loadKnowledge" value="_load_knowledge_base()&#10;Load JSON/CSV databases" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="1">
          <mxGeometry x="680" y="320" width="160" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="setupGUI" value="setup_gui()&#10;Create colored emoji interface" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="1">
          <mxGeometry x="680" y="420" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- READY STATE -->
        <mxCell id="ready" value="READY STATE&#10;GUI displayed&#10;Waiting for user input" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="680" y="520" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- USER ACTION -->
        <mxCell id="userAction" value="User Clicks Button?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="700" y="640" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- SINGLE SEARCH PATH -->
        <mxCell id="singleSearch" value="🔍 Search Component&#10;Button Clicked" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="400" y="780" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="startSearch" value="start_search()&#10;Validate inputs&#10;Create thread" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="400" y="880" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="searchComponent" value="search_component()&#10;Main search orchestrator" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="1">
          <mxGeometry x="400" y="1000" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- DATASHEET SEARCH -->
        <mxCell id="datasheetSearch" value="search_datasheet_enhanced()&#10;Import datasheet_finder&#10;Call find_datasheet_combined()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="1">
          <mxGeometry x="400" y="1100" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="datasheetDecision" value="Datasheet&#10;Found?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="1">
          <mxGeometry x="410" y="1220" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="datasheetFallback" value="search_datasheet_fallback()&#10;Try Digi-Key API&#10;Try Mouser API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="1">
          <mxGeometry x="200" y="1320" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="datasheetSuccess" value="Datasheet Downloaded&#10;Extract package type&#10;Save to datasheets/" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="600" y="1220" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- 3D MODEL SEARCH -->
        <mxCell id="stepSearch" value="search_step_enhanced()&#10;Import step_finder&#10;Call find_step_file()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;" vertex="1" parent="1">
          <mxGeometry x="600" y="1340" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="stepDecision" value="STEP File&#10;Found?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;" vertex="1" parent="1">
          <mxGeometry x="610" y="1460" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="stepFallback" value="search_alternative_sources()&#10;Try manufacturer website&#10;Check WURTH parts" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;" vertex="1" parent="1">
          <mxGeometry x="400" y="1560" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="stepSuccess" value="STEP File Downloaded&#10;Save to 3d/ directory" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="800" y="1460" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- EXCEL PATH -->
        <mxCell id="excelSearch" value="📊 Load Excel File&#10;Button Clicked" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;" vertex="1" parent="1">
          <mxGeometry x="1000" y="780" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="loadExcel" value="load_excel_file()&#10;Read columns F &amp; G" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;" vertex="1" parent="1">
          <mxGeometry x="1000" y="880" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="processExcel" value="process_all_matrix_parts()&#10;Loop through rows&#10;Check columns H &amp; K" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;" vertex="1" parent="1">
          <mxGeometry x="1000" y="980" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="updateExcel" value="Update Excel File:&#10;Column H: datasheet path&#10;Column K: STEP filename" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="1000" y="1100" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- RESULTS -->
        <mxCell id="logResults" value="Log to found-files-log.csv&#10;Update knowledge base&#10;Update manufacturer CSV" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="680" y="1680" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="displayResults" value="Display Results in GUI&#10;Show colored emoji status&#10;Enable buttons" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="680" y="1800" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- ARROWS -->
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="start" target="setupEmoji">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="setupEmoji" target="createDirs">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="createDirs" target="loadKnowledge">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="loadKnowledge" target="setupGUI">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="setupGUI" target="ready">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="ready" target="userAction">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="userAction" target="singleSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="userAction" target="excelSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="singleSearch" target="startSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="startSearch" target="searchComponent">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="searchComponent" target="datasheetSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetSearch" target="datasheetDecision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetDecision" target="datasheetSuccess">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetDecision" target="datasheetFallback">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetSuccess" target="stepSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepSearch" target="stepDecision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepDecision" target="stepSuccess">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepDecision" target="stepFallback">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="excelSearch" target="loadExcel">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="loadExcel" target="processExcel">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="processExcel" target="updateExcel">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepSuccess" target="logResults">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="updateExcel" target="logResults">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="logResults" target="displayResults">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;entryX=1;entryY=0.5;" edge="1" parent="1" source="displayResults" target="ready">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="600" y="1840" />
              <mxPoint x="600" y="560" />
            </Array>
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
