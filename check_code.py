#!/usr/bin/env python3
"""Check for coding problems in component_finder.py"""

import re

def check_code_problems():
    print("Checking component_finder.py for coding problems...")
    print("=" * 50)
    
    try:
        with open('component_finder.py', 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"Error reading file: {e}")
        return
    
    # Check 1: Find duplicate function definitions
    functions = {}
    for i, line in enumerate(lines, 1):
        if line.strip().startswith('def '):
            func_match = re.match(r'\s*def\s+(\w+)\s*\(', line)
            if func_match:
                func_name = func_match.group(1)
                if func_name in functions:
                    print(f"DUPLICATE FUNCTION: {func_name}")
                    print(f"  First definition: line {functions[func_name]}")
                    print(f"  Second definition: line {i}")
                    print()
                else:
                    functions[func_name] = i
    
    # Check 2: Find unreachable code (code after return statements)
    print("Checking for unreachable code...")
    for i, line in enumerate(lines, 1):
        if 'return ' in line and i < len(lines):
            # Check if there's more code in the same function after return
            indent_level = len(line) - len(line.lstrip())
            for j in range(i, min(i + 5, len(lines))):
                next_line = lines[j]
                if next_line.strip() and not next_line.strip().startswith('#'):
                    next_indent = len(next_line) - len(next_line.lstrip())
                    if next_indent > indent_level:
                        print(f"POSSIBLE UNREACHABLE CODE after line {i}: {line.strip()}")
                        print(f"  Next line {j+1}: {next_line.strip()}")
                        break
    
    # Check 3: Find inconsistent indentation
    print("\nChecking for indentation issues...")
    for i, line in enumerate(lines, 1):
        if line.strip() and not line.startswith(' ') and not line.startswith('\t'):
            continue  # Top level is fine
        
        # Check for mixed tabs and spaces
        if '\t' in line and ' ' in line[:len(line) - len(line.lstrip())]:
            print(f"MIXED TABS AND SPACES at line {i}: {repr(line[:20])}")
    
    # Check 4: Find variables used before definition
    print("\nChecking for obvious variable issues...")
    for i, line in enumerate(lines, 1):
        if 'valid_rows' in line and 'len(valid_rows)' in line:
            print(f"SUSPICIOUS: 'valid_rows' usage at line {i}: {line.strip()}")
    
    print("\nCode check complete.")

if __name__ == "__main__":
    check_code_problems()
