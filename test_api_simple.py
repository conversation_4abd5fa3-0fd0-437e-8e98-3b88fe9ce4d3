#!/usr/bin/env python3
"""
Simple test program to verify Digi-Key API functionality
This runs independently while debugging the main component_finder.py
"""

import sys
import json
from datetime import datetime

def log_message(message, level="INFO"):
    """Simple logging function"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    if level == "ERROR":
        print(f"❌ [{timestamp}] ERROR: {message}")
    elif level == "SUCCESS":
        print(f"✅ [{timestamp}] SUCCESS: {message}")
    elif level == "WARNING":
        print(f"⚠️ [{timestamp}] WARNING: {message}")
    else:
        print(f"ℹ️ [{timestamp}] INFO: {message}")

def test_api_credentials():
    """Test if API credentials are available and valid"""
    try:
        log_message("Testing API credentials...")
        
        # Try to load credentials
        with open('digikey_api_credentials.json', 'r') as f:
            credentials = json.load(f)
        
        required_keys = ['client_id', 'client_secret', 'access_token']
        for key in required_keys:
            if key not in credentials:
                log_message(f"Missing credential: {key}", "ERROR")
                return False
            elif not credentials[key]:
                log_message(f"Empty credential: {key}", "ERROR")
                return False
        
        log_message("All required credentials found", "SUCCESS")
        log_message(f"Client ID: {credentials['client_id'][:20]}...")
        log_message(f"Access Token: {credentials['access_token'][:20]}...")
        
        return credentials
        
    except FileNotFoundError:
        log_message("Credentials file not found", "ERROR")
        return False
    except json.JSONDecodeError:
        log_message("Invalid JSON in credentials file", "ERROR")
        return False
    except Exception as e:
        log_message(f"Error loading credentials: {e}", "ERROR")
        return False

def test_api_import():
    """Test if the API module can be imported"""
    try:
        log_message("Testing API module import...")
        from digikey_datasheet_improved import DigikeyDatasheetFinder
        log_message("API module imported successfully", "SUCCESS")
        return DigikeyDatasheetFinder
    except ImportError as e:
        log_message(f"Cannot import API module: {e}", "ERROR")
        return False
    except Exception as e:
        log_message(f"Error importing API module: {e}", "ERROR")
        return False

def test_api_search(manufacturer="Texas Instruments", part_number="LM358N"):
    """Test the API search functionality"""
    log_message(f"Testing API search for {manufacturer} {part_number}...")
    
    # Test credentials
    credentials = test_api_credentials()
    if not credentials:
        return False
    
    # Test import
    DigikeyDatasheetFinder = test_api_import()
    if not DigikeyDatasheetFinder:
        return False
    
    try:
        # Create API finder
        log_message("Creating API finder instance...")
        api_finder = DigikeyDatasheetFinder()
        
        # Load credentials
        log_message("Loading credentials...")
        if not api_finder.load_credentials():
            log_message("Failed to load credentials", "ERROR")
            return False
        
        log_message("Credentials loaded successfully", "SUCCESS")
        
        # Perform search
        log_message(f"Searching for: {manufacturer} {part_number}")
        results = api_finder.search_part(manufacturer, part_number)
        
        # Check results
        if results is None:
            log_message("API returned None", "WARNING")
            return False
        else:
            log_message(f"API returned results of type: {type(results)}", "SUCCESS")
            log_message(f"API results keys: {list(results.keys()) if isinstance(results, dict) else 'Not a dict'}")

            # Handle the actual API response structure
            if isinstance(results, dict):
                # The API returns a single product dictionary directly
                first_result = results
                log_message("Using single product result", "SUCCESS")
            elif isinstance(results, list):
                if len(results) == 0:
                    log_message("API returned empty list", "WARNING")
                    return False
                else:
                    log_message(f"API returned {len(results)} results", "SUCCESS")
                    first_result = results[0]
            else:
                log_message(f"Unexpected results type: {type(results)}", "ERROR")
                return False
            log_message("Product details:")

            if 'ManufacturerProductNumber' in first_result:
                log_message(f"  Part Number: {first_result['ManufacturerProductNumber']}")

            if 'Manufacturer' in first_result:
                log_message(f"  Manufacturer: {first_result['Manufacturer']}")

            # Check for datasheet - the API uses 'DatasheetUrl'
            datasheet_url = None
            if 'DatasheetUrl' in first_result:
                datasheet_url = first_result['DatasheetUrl']
                log_message(f"  Datasheet URL: {datasheet_url}", "SUCCESS")
            elif 'PrimaryDatasheet' in first_result:
                datasheet_url = first_result['PrimaryDatasheet']
                log_message(f"  Primary Datasheet: {datasheet_url}", "SUCCESS")
            else:
                log_message("  No datasheet found in result", "WARNING")

            # Show other useful info
            if 'Description' in first_result and first_result['Description']:
                desc = str(first_result['Description'])
                log_message(f"  Description: {desc[:100]}...")

            if 'ProductUrl' in first_result:
                log_message(f"  Product URL: {first_result['ProductUrl']}")
            
            return True
            
    except Exception as e:
        log_message(f"API search error: {e}", "ERROR")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🧪 DIGI-KEY API TEST PROGRAM")
    print("=" * 60)
    
    # Get test parameters
    if len(sys.argv) >= 3:
        manufacturer = sys.argv[1]
        part_number = sys.argv[2]
    else:
        manufacturer = "Texas Instruments"
        part_number = "LM358N"
    
    log_message(f"Testing with: {manufacturer} {part_number}")
    print("-" * 60)
    
    # Run tests
    success = test_api_search(manufacturer, part_number)
    
    print("-" * 60)
    if success:
        log_message("🎉 API TEST PASSED!", "SUCCESS")
    else:
        log_message("💥 API TEST FAILED!", "ERROR")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
