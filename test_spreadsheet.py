import pandas as pd

# Load the spreadsheet
df = pd.read_excel('Teledyne_Flir_master-footprint_list-tura.xlsx')

print("Spreadsheet Analysis:")
print("=" * 50)
print(f"Shape: {df.shape}")
print(f"Columns: {list(df.columns)}")

print("\nFirst few rows with actual data:")
for i in range(min(20, len(df))):
    row = df.iloc[i]
    # Check if row has any non-null values
    non_null_values = row.dropna()
    if len(non_null_values) > 0:
        print(f"\nRow {i+1}:")
        for col, val in non_null_values.items():
            if pd.notna(val) and str(val).strip() != '':
                print(f"  {col}: {val}")

print("\nLooking for manufacturer and part number data:")
# Look for columns that might contain manufacturer/part data
for col in df.columns:
    if 'manufacturer' in col.lower() or 'part' in col.lower():
        non_null = df[col].dropna()
        if len(non_null) > 0:
            print(f"\n{col}:")
            for i, val in enumerate(non_null.head(10)):
                if pd.notna(val) and str(val).strip() != '':
                    print(f"  Row {non_null.index[i]+1}: {val}")
