<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T12:00:00.000Z" agent="5.0" version="22.1.11" etag="abc123" type="device">
  <diagram name="Component Finder - Ultra Detailed Step-by-Step" id="flowchart">
    <mxGraphModel dx="2000" dy="1200" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2400" pageHeight="4000" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- PROGRAM INITIALIZATION SEQUENCE -->
        <mxCell id="main" value="main()&#10;print('🚀 Starting Component Finder GUI...')&#10;print(f'📁 Working directory: {os.getcwd()}')" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1100" y="20" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="createRoot" value="root = tk.Tk()&#10;print('✅ Root window created')&#10;root.lift()&#10;root.attributes('-topmost', True)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1100" y="140" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="initGUI" value="app = ComponentFinderGUI(root)&#10;print('🔧 Initializing ComponentFinderGUI...')&#10;__init__(self, root)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1100" y="260" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="setupEmoji" value="_setup_emoji_images()&#10;Create emoji_images/ directory&#10;Download 25 PNG files from Twitter&#10;rocket.png, check.png, cross.png..." style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1100" y="380" width="200" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="createDirs" value="Create Directories:&#10;self.datasheet_dir = Path('datasheets')&#10;self.model_3d_dir = Path('3d')&#10;self.datasheet_dir.mkdir(exist_ok=True)&#10;self.model_3d_dir.mkdir(exist_ok=True)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1100" y="520" width="200" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="initSession" value="self.session = self._create_session()&#10;Create requests.Session()&#10;Set headers, timeout, retries&#10;Configure SSL verification" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1100" y="680" width="200" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="loadKnowledge" value="_load_knowledge_base()&#10;Load manufacturer_knowledge.json&#10;Load actual-web-site-xref.csv&#10;Initialize empty if not exist" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1100" y="820" width="200" height="100" as="geometry" />
        </mxCell>
        
        <mxCell id="setupGUI" value="setup_gui()&#10;Create main window layout&#10;Create input fields&#10;Create 8 colored emoji buttons&#10;Create scrollable text area&#10;Configure emoji image display" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1100" y="960" width="200" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="testEmoji" value="_test_emoji_colors()&#10;Test emoji image system&#10;Display startup messages&#10;Show available fonts" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1100" y="1120" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="ready" value="READY STATE&#10;root.mainloop()&#10;GUI displayed and responsive&#10;Waiting for user button clicks" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#2e7d32;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1100" y="1240" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- USER ACTION DECISION -->
        <mxCell id="userAction" value="User Clicks Button?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1140" y="1380" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- SINGLE COMPONENT SEARCH DETAILED FLOW -->
        <mxCell id="clickSearch" value="🔍 Search Component&#10;Button Clicked&#10;self.search_button clicked" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="1520" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="startSearch" value="start_search()&#10;manufacturer = self.manufacturer_entry.get()&#10;part_number = self.part_number_entry.get()&#10;Validate inputs not empty&#10;self.search_button.config(state='disabled')&#10;self.status_text.set('Searching...')" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="1640" width="160" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="createThread" value="Create Thread:&#10;search_thread = threading.Thread(&#10;  target=self.search_component,&#10;  args=(manufacturer, part_number)&#10;)&#10;search_thread.daemon = True&#10;search_thread.start()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="1820" width="160" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="searchComponent" value="search_component()&#10;Running in separate thread&#10;try/except wrapper&#10;self.add_comment(f'🔍 Searching for {part_number}')&#10;self.add_comment(f'🏭 Manufacturer: {manufacturer}')" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="2000" width="160" height="120" as="geometry" />
        </mxCell>
        
        <!-- DISTRIBUTOR VALIDATION STEP -->
        <mxCell id="validateDistributors" value="validate_with_distributors()&#10;self.add_comment('📡 Step 1: Validating with distributors...')&#10;Try Digi-Key first&#10;Try Mouser second&#10;Get verified manufacturer name&#10;Get official website URL" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="2160" width="160" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="digikeyValidation" value="search_digikey_for_website()&#10;search_url = f'https://www.digikey.com/en/products/filter'&#10;Try simple search first&#10;Parse search results&#10;Extract manufacturer website&#10;Return verified info" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="200" y="2340" width="160" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="mouserValidation" value="search_mouser_for_website()&#10;search_url = f'https://www.mouser.com/ProductDetail'&#10;Try simple search&#10;Parse product page&#10;Extract manufacturer info&#10;Return website URL" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="2340" width="160" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="validationDecision" value="Website&#10;Found?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="430" y="2500" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- DATASHEET SEARCH DETAILED FLOW -->
        <mxCell id="datasheetSearch" value="search_datasheet_enhanced()&#10;self.add_comment('📄 Searching for datasheet...')&#10;Import datasheet_finder module&#10;Call find_datasheet_combined()&#10;Pass manufacturer, part_number, website" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="600" y="2340" width="160" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="datasheetEnhanced" value="find_datasheet_combined()&#10;Try Digi-Key API search&#10;Try Mouser API search&#10;Try manufacturer website&#10;Try Google search&#10;Return (url, filename) or None" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="600" y="2500" width="160" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="datasheetDecision" value="Datasheet&#10;Found?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="630" y="2660" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="datasheetFallback" value="search_datasheet_fallback()&#10;self.add_comment('🔄 Trying fallback methods...')&#10;search_distributors_for_part_info()&#10;search_digikey_simple()&#10;search_mouser_simple()&#10;Manual learning mode if all fail" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="2760" width="160" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="datasheetLearning" value="learn_datasheet_pattern()&#10;self.add_comment('🎓 Entering learning mode...')&#10;Open browser to search page&#10;Show learning dialog&#10;Wait for user to find download&#10;Learn pattern and save" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="200" y="2760" width="160" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="datasheetSuccess" value="Datasheet Downloaded&#10;self.add_comment(f'✅ Downloaded: {filename}')&#10;Save to datasheets/ directory&#10;Extract package type from PDF&#10;Update status fields" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="800" y="2660" width="160" height="120" as="geometry" />
        </mxCell>
        
        <!-- 3D MODEL SEARCH ULTRA DETAILED FLOW -->
        <mxCell id="stepSearch" value="search_step_enhanced()&#10;self.add_comment('🔧 Searching for 3D STEP files...')&#10;Import step_finder module&#10;Call find_step_file()&#10;Pass manufacturer, part_number, website" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="800" y="2820" width="160" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="stepEnhanced" value="find_step_file()&#10;Try manufacturer website first&#10;Check for WURTH parts&#10;Try UltraLibrarian&#10;Try SnapMagic/SnapEDA&#10;Try SamacSys&#10;Return (url, filename, source) or None" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="800" y="2980" width="160" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="stepDecision" value="STEP File&#10;Found?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="830" y="3160" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="stepFallback" value="search_alternative_step_sources()&#10;self.add_comment('🔄 Trying alternative sources...')&#10;Check if WURTH part&#10;search_wurth_simple() if WURTH&#10;search_manufacturer_for_step()&#10;Show user dialog for alternatives" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="600" y="3260" width="160" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="wurthCheck" value="'wurth' in&#10;manufacturer.lower()?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="630" y="3440" width="100" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="wurthSearch" value="search_wurth_simple()&#10;self.add_comment('🔍 Using WURTH search...')&#10;search_url = 'https://www.we-online.com'&#10;Try direct part number search&#10;Download STEP file if found" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="800" y="3440" width="160" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="alternativeDialog" value="Show Alternative Sources Dialog:&#10;messagebox.askyesno()&#10;'STEP file not found on manufacturer website'&#10;'Search UltraLibrarian, SnapMagic, SamacSys?'&#10;YES = Continue, NO = Skip" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="3440" width="160" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="alternativeDecision" value="User Chose&#10;YES?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="430" y="3620" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- ALTERNATIVE SOURCES DETAILED -->
        <mxCell id="ultraLibrarianSearch" value="search_ultralibrarian_with_learning()&#10;self.add_comment('🔍 Searching UltraLibrarian...')&#10;search_url = 'https://www.ultralibrarian.com'&#10;Try learned patterns first&#10;Try automatic search&#10;Enter learning mode if part found but download fails" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="200" y="3720" width="160" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="snapMagicSearch" value="search_snapmagic_with_learning()&#10;self.add_comment('🔍 Searching SnapMagic/SnapEDA...')&#10;Try snapeda.com and snapmagic.com&#10;apply_learned_patterns() first&#10;search_snapmagic() automatic&#10;learn_download_pattern() if needed" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="400" y="3720" width="160" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="samacSysSearch" value="search_samacsys_with_learning()&#10;self.add_comment('🔍 Searching SamacSys...')&#10;search_url = 'https://componentsearchengine.com'&#10;Try learned patterns&#10;Try automatic search&#10;Enter learning mode if needed" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="600" y="3720" width="160" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="stepLearning" value="Learning Mode for 3D:&#10;learn_download_pattern()&#10;Open browser to part page&#10;Show learning dialog&#10;'Please find and click download link'&#10;Learn pattern and save to JSON" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#c62828;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="800" y="3720" width="160" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="stepSuccess" value="STEP File Downloaded&#10;self.add_comment(f'✅ Downloaded: {filename}')&#10;Save to 3d/ directory&#10;filename = f'{part_number}.step'&#10;Update 3D model status field" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1000" y="3160" width="160" height="120" as="geometry" />
        </mxCell>
        
        <!-- RESULTS AND LOGGING DETAILED -->
        <mxCell id="logResults" value="Log to found-files-log.csv:&#10;Write row with:&#10;- Manufacturer Name&#10;- Part Number&#10;- Datasheet URL&#10;- Datasheet Filename&#10;- 3D Model URL&#10;- 3D Model Filename&#10;- Package Type&#10;- Date Found&#10;- Search Success (True/False)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1000" y="3320" width="160" height="180" as="geometry" />
        </mxCell>
        
        <mxCell id="updateKnowledge" value="Update Knowledge Bases:&#10;manufacturer_knowledge.json:&#10;- Add/update manufacturer entry&#10;- Save successful patterns&#10;- Update success counts&#10;actual-web-site-xref.csv:&#10;- Add manufacturer website&#10;download_patterns.json:&#10;- Save learned download patterns" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1000" y="3540" width="160" height="180" as="geometry" />
        </mxCell>
        
        <mxCell id="displayResults" value="show_final_results()&#10;self.add_comment('🎉 SEARCH COMPLETE')&#10;Display manufacturer, part number&#10;Show datasheet status&#10;Show 3D model status&#10;Update GUI status fields&#10;self.datasheet_status.set()&#10;self.model_3d_status.set()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1000" y="3760" width="160" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="searchComplete" value="search_complete()&#10;self.root.after(0, self.search_complete)&#10;Re-enable search button&#10;self.search_button.config(state='normal')&#10;self.status_text.set('Ready')&#10;Return to ready state" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1000" y="3960" width="160" height="140" as="geometry" />
        </mxCell>
        
        <!-- INITIALIZATION ARROWS -->
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="main" target="createRoot">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="createRoot" target="initGUI">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="initGUI" target="setupEmoji">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="setupEmoji" target="createDirs">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="createDirs" target="initSession">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="initSession" target="loadKnowledge">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="loadKnowledge" target="setupGUI">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="setupGUI" target="testEmoji">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="testEmoji" target="ready">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="ready" target="userAction">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- SINGLE SEARCH FLOW ARROWS -->
        <mxCell id="arrow11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="userAction" target="clickSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="clickSearch" target="startSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="startSearch" target="createThread">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="createThread" target="searchComponent">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="searchComponent" target="validateDistributors">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- VALIDATION FLOW ARROWS -->
        <mxCell id="arrow16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="validateDistributors" target="digikeyValidation">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="validateDistributors" target="mouserValidation">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="digikeyValidation" target="validationDecision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="mouserValidation" target="validationDecision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="validationDecision" target="datasheetSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- DATASHEET SEARCH ARROWS -->
        <mxCell id="arrow21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetSearch" target="datasheetEnhanced">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetEnhanced" target="datasheetDecision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetDecision" target="datasheetSuccess">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetDecision" target="datasheetFallback">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetFallback" target="datasheetLearning">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 3D MODEL SEARCH ARROWS -->
        <mxCell id="arrow26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="datasheetSuccess" target="stepSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepSearch" target="stepEnhanced">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepEnhanced" target="stepDecision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepDecision" target="stepSuccess">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepDecision" target="stepFallback">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepFallback" target="wurthCheck">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="wurthCheck" target="wurthSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="wurthCheck" target="alternativeDialog">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="alternativeDialog" target="alternativeDecision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="alternativeDecision" target="ultraLibrarianSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="alternativeDecision" target="snapMagicSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="alternativeDecision" target="samacSysSearch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="ultraLibrarianSearch" target="stepLearning">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow39" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="snapMagicSearch" target="stepLearning">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow40" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="samacSysSearch" target="stepLearning">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- RESULTS FLOW ARROWS -->
        <mxCell id="arrow41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="stepSuccess" target="logResults">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow42" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="logResults" target="updateKnowledge">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow43" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="updateKnowledge" target="displayResults">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="displayResults" target="searchComplete">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- RETURN TO READY -->
        <mxCell id="arrow45" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;entryX=1;entryY=0.5;" edge="1" parent="1" source="searchComplete" target="ready">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1400" y="4030" />
              <mxPoint x="1400" y="1290" />
            </Array>
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
