DRAW.IO FLOWCHART IMPORT - STEP BY STEP GUIDE
==============================================

PROBLEM: Draw.io's direct Mermaid import doesn't work well.

SOLUTION: Use Mermaid Live Editor to convert to SVG, then import SVG.

STEP-BY-STEP PROCESS:
====================

1. CONVERT MERMAID TO SVG:
   - Go to: https://mermaid.live/
   - Clear the default diagram
   - Paste your flowchart code (from component_finder_flowchart.mmd)
   - The diagram will render automatically
   - Click "Actions" → "Export SVG"
   - Save as "Component_Finder_Flowchart.svg"

2. IMPORT SVG INTO DRAW.IO:
   - Open Draw.io Desktop
   - File → Import
   - Select your saved SVG file
   - The flowchart will import as a visual diagram

3. EDIT IN DRAW.IO:
   - Now you can edit colors, shapes, text
   - Drag and drop to rearrange
   - Add new elements
   - Export as PDF: File → Export as → PDF

ALTERNATIVE - ONLINE DRAW.IO WITH MERMAID:
==========================================
   - Go to: https://app.diagrams.net/
   - Extras → Plugins → Add → Mermaid
   - Insert → Advanced → Mermaid
   - Paste your code directly

YOUR FILES:
===========
- Mermaid code: component_finder_flowchart.mmd
- Full documentation: Component_Finder_Flowchart.md
- This guide: drawio_import_guide.txt
