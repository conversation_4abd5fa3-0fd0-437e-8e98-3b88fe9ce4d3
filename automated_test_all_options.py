#!/usr/bin/env python3
"""
Automated test version with all options enabled
Reads spreadsheet and processes all parts automatically
"""

import pandas as pd
import sys
import traceback
import threading
import time
from unittest.mock import patch, MagicMock

def create_automated_component_finder():
    """Create a ComponentFinderGUI with all options enabled and automated responses"""

    # Mock tkinter to prevent GUI from opening
    with patch('tkinter.Tk') as mock_tk:
        with patch('tkinter.messagebox') as mock_msgbox:
            with patch('tkinter.simpledialog') as mock_simpledialog:
                    
                    # Import after patching
                    import component_finder
                    
                    # Create mock root
                    mock_root = MagicMock()
                    mock_tk.return_value = mock_root
                    
                    # Create the component finder
                    finder = component_finder.ComponentFinderGUI(mock_root)
                    
                    # Enable ALL datasheet options
                    finder.search_datasheet_digikey = MagicMock()
                    finder.search_datasheet_digikey.get.return_value = True
                    finder.search_datasheet_mouser = MagicMock()
                    finder.search_datasheet_mouser.get.return_value = True
                    finder.search_datasheet_rs = MagicMock()
                    finder.search_datasheet_rs.get.return_value = True
                    finder.search_datasheet_generic = MagicMock()
                    finder.search_datasheet_generic.get.return_value = True
                    
                    # Enable ALL 3D model options
                    finder.search_3d_manufacturer = MagicMock()
                    finder.search_3d_manufacturer.get.return_value = True
                    finder.search_3d_ultralibrarian = MagicMock()
                    finder.search_3d_ultralibrarian.get.return_value = True
                    finder.search_3d_samacsys = MagicMock()
                    finder.search_3d_samacsys.get.return_value = True
                    finder.search_3d_snapeda = MagicMock()
                    finder.search_3d_snapeda.get.return_value = True
                    
                    # Mock dialogs to automatically continue
                    mock_msgbox.askyesnocancel.return_value = True  # Always continue
                    mock_simpledialog.askstring.return_value = None  # No part correction
                    
                    # Mock file operations
                    finder.update_matrix_cell = MagicMock()
                    
                    # Capture all comments for analysis
                    comments = []
                    original_add_comment = finder.add_comment
                    
                    def capture_comment(message):
                        comments.append(f"{len(comments)+1:3d}. {message}")
                        print(f"   {len(comments):3d}. {message}")
                    
                    finder.add_comment = capture_comment
                    finder.captured_comments = comments
                    
                    return finder

def load_spreadsheet_parts():
    """Load all parts from the spreadsheet"""
    try:
        excel_file = "Teledyne_Flir_master-footprint_list-tura.xlsx"
        df = pd.read_excel(excel_file)
        
        manufacturer_col = 'Manufacturer Name '
        part_col = 'Manufacturer full part number'
        
        parts = []
        for idx, row in df.iterrows():
            mfg = str(row[manufacturer_col]).strip() if pd.notna(row[manufacturer_col]) else ""
            part = str(row[part_col]).strip() if pd.notna(row[part_col]) else ""
            
            if mfg and part and mfg != 'nan' and part != 'nan':
                parts.append({
                    'row': idx + 1,
                    'manufacturer': mfg,
                    'part_number': part
                })
        
        return parts
    except Exception as e:
        print(f"❌ Error loading spreadsheet: {e}")
        return []

def analyze_part_results(part_info, comments):
    """Analyze the results for a single part"""
    analysis = {
        'part': part_info,
        'datasheet_found': False,
        'datasheet_source': None,
        'part_validated': False,
        'step_found': False,
        'step_source': None,
        'errors': [],
        'warnings': []
    }
    
    for comment in comments:
        comment_text = comment.lower()
        
        # Check datasheet results
        if 'found datasheet!' in comment_text:
            analysis['datasheet_found'] = True
            if 'digikey' in comment_text:
                analysis['datasheet_source'] = 'Digikey'
            elif 'mouser' in comment_text:
                analysis['datasheet_source'] = 'Mouser'
            elif 'rs' in comment_text:
                analysis['datasheet_source'] = 'RS Components'
        
        # Check part validation
        if 'part number' in comment_text and 'found in datasheet' in comment_text:
            analysis['part_validated'] = True
        
        # Check STEP file results
        if 'found step file' in comment_text or 'step file:' in comment_text:
            analysis['step_found'] = True
            if 'ultralibrarian' in comment_text:
                analysis['step_source'] = 'UltraLibrarian'
            elif 'samacsys' in comment_text:
                analysis['step_source'] = 'SamacSys'
            elif 'snapeda' in comment_text:
                analysis['step_source'] = 'SnapEDA'
            elif 'manufacturer' in comment_text:
                analysis['step_source'] = 'Manufacturer'
        
        # Check for errors
        if comment.startswith('   ') and ('❌' in comment or 'error' in comment_text):
            analysis['errors'].append(comment.strip())
        
        # Check for warnings
        if comment.startswith('   ') and ('⚠️' in comment or 'warning' in comment_text):
            analysis['warnings'].append(comment.strip())
    
    return analysis

def test_all_parts_automated():
    """Test all parts from spreadsheet with all options enabled"""
    print("🚀 AUTOMATED TEST - ALL OPTIONS ENABLED")
    print("=" * 80)
    
    # Load parts
    parts = load_spreadsheet_parts()
    if not parts:
        print("❌ No parts loaded from spreadsheet")
        return False
    
    print(f"📊 Loaded {len(parts)} parts from spreadsheet")
    for part in parts:
        print(f"   Row {part['row']:2d}: {part['manufacturer']} {part['part_number']}")
    
    print("\n" + "=" * 80)
    print("🧪 PROCESSING ALL PARTS...")
    print("=" * 80)
    
    # Create automated component finder
    finder = create_automated_component_finder()
    
    # Set up Excel data
    excel_data = []
    for part in parts:
        excel_data.append({
            'Manufacturer Name ': part['manufacturer'],
            'Manufacturer full part number': part['part_number'],
            'Datasheet': '',
            'STEP Source': '',
            'STEP File': ''
        })
    
    finder.excel_df = pd.DataFrame(excel_data)
    finder.column_mapping = {
        'manufacturer': 'Manufacturer Name ',
        'part_number': 'Manufacturer full part number',
        'datasheet': 'Datasheet',
        'step_source': 'STEP Source',
        'step_file': 'STEP File'
    }
    
    # Process each part
    results = []
    for i, part in enumerate(parts):
        print(f"\n{'='*20} PART {i+1}/{len(parts)} {'='*20}")
        print(f"🔍 Processing: {part['manufacturer']} {part['part_number']}")
        print("-" * 60)
        
        # Clear comments for this part
        finder.captured_comments.clear()
        
        try:
            # Process the part with timeout
            def process_part():
                finder.process_matrix_row(i, part['manufacturer'], part['part_number'])
            
            thread = threading.Thread(target=process_part)
            thread.daemon = True
            thread.start()
            
            # Wait with timeout
            timeout = 60  # 1 minute per part
            for j in range(timeout):
                if not thread.is_alive():
                    break
                time.sleep(1)
                if j % 15 == 14:  # Print every 15 seconds
                    print(f"   ⏳ Still processing... ({j+1}s)")
            
            if thread.is_alive():
                print(f"   ❌ TIMEOUT after {timeout}s")
                analysis = {
                    'part': part,
                    'status': 'TIMEOUT',
                    'datasheet_found': False,
                    'step_found': False,
                    'errors': [f'Timeout after {timeout}s']
                }
            else:
                # Analyze results
                analysis = analyze_part_results(part, finder.captured_comments)
                analysis['status'] = 'COMPLETED'
                
                print(f"   📊 Results:")
                print(f"      Datasheet: {'✅' if analysis['datasheet_found'] else '❌'} {analysis['datasheet_source'] or 'Not found'}")
                print(f"      Part Valid: {'✅' if analysis['part_validated'] else '❌'}")
                print(f"      STEP File: {'✅' if analysis['step_found'] else '❌'} {analysis['step_source'] or 'Not found'}")
                if analysis['errors']:
                    print(f"      Errors: {len(analysis['errors'])}")
                if analysis['warnings']:
                    print(f"      Warnings: {len(analysis['warnings'])}")
            
            results.append(analysis)
            
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            analysis = {
                'part': part,
                'status': 'EXCEPTION',
                'datasheet_found': False,
                'step_found': False,
                'errors': [str(e)]
            }
            results.append(analysis)
    
    # Final summary
    print("\n" + "=" * 80)
    print("📊 FINAL SUMMARY")
    print("=" * 80)
    
    completed = sum(1 for r in results if r['status'] == 'COMPLETED')
    timeouts = sum(1 for r in results if r['status'] == 'TIMEOUT')
    exceptions = sum(1 for r in results if r['status'] == 'EXCEPTION')
    
    datasheets_found = sum(1 for r in results if r.get('datasheet_found', False))
    steps_found = sum(1 for r in results if r.get('step_found', False))
    
    print(f"📈 Processing Status:")
    print(f"   ✅ Completed: {completed}/{len(parts)}")
    print(f"   ⏱️ Timeouts: {timeouts}/{len(parts)}")
    print(f"   ❌ Exceptions: {exceptions}/{len(parts)}")
    
    print(f"\n📈 Success Rates:")
    print(f"   📄 Datasheets found: {datasheets_found}/{len(parts)} ({datasheets_found/len(parts)*100:.1f}%)")
    print(f"   🎯 STEP files found: {steps_found}/{len(parts)} ({steps_found/len(parts)*100:.1f}%)")
    
    # Detailed results
    print(f"\n📋 DETAILED RESULTS:")
    for result in results:
        part = result['part']
        status_icon = "✅" if result['status'] == 'COMPLETED' else "❌"
        datasheet_icon = "📄" if result.get('datasheet_found') else "❌"
        step_icon = "🎯" if result.get('step_found') else "❌"
        
        print(f"   {status_icon} Row {part['row']:2d}: {part['manufacturer']} {part['part_number']}")
        print(f"      {datasheet_icon} Datasheet: {result.get('datasheet_source', 'Not found')}")
        print(f"      {step_icon} STEP: {result.get('step_source', 'Not found')}")
        if result.get('errors'):
            print(f"      ❌ Errors: {len(result['errors'])}")
    
    return timeouts == 0 and exceptions == 0

if __name__ == "__main__":
    success = test_all_parts_automated()
    if success:
        print(f"\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
    else:
        print(f"\n❌ SOME ISSUES FOUND - CHECK RESULTS ABOVE")
