ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('STEP AP214'),'1');
FILE_NAME('DIP-8_STM','2025-09-06T10:03:27',(''),(''),'','','');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN'));
ENDSEC;
DATA;
#1=SHAPE_DEFINITION_REPRESENTATION(#2,#3);
#2=PRODUCT_DEFINITION_SHAPE('',$,#4);
#3=SHAPE_REPRESENTATION('',(#188,#244,#933,#1644,#2355,#3066,#3777,#4488,#5199,#5910,#19),#11);
#4=PRODUCT_DEFINITION('design','example product_definition',#6,#5);
#5=PRODUCT_DEFINITION_CONTEXT('3D Mechanical Parts',#10,'design');
#6=PRODUCT_DEFINITION_FORMATION('1.0','first version',#8);
#7=APPLICATION_PROTOCOL_DEFINITION('international standard','automotive_design',2003,#10);
#8=PRODUCT('product','part','',(#9));
#9=PRODUCT_CONTEXT('3D Mechanical Parts',#10,'mechanical');
#10=APPLICATION_CONTEXT('Core Data for Automotive Mechanical Design Process');
#11=(GEOMETRIC_REPRESENTATION_CONTEXT(3) GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#12)) GLOBAL_UNIT_ASSIGNED_CONTEXT((#13,#14,#18)) REPRESENTATION_CONTEXT('ID1','3D'));
#12=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-005),#13,'DISTANCE_ACCURACY_VALUE','Maximum model space distance between geometric entities at asserted connectivities');
#13=(LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.));
#14=(CONVERSION_BASED_UNIT('degree',#16) NAMED_UNIT(#15) PLANE_ANGLE_UNIT());
#15=DIMENSIONAL_EXPONENTS(0.,0.,0.,0.,0.,0.,0.);
#16=MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(0.01745329252),#17);
#17=(NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.));
#18=(NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT());
#19=AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20=CARTESIAN_POINT('',(0.0,0.0,0.0));
#21=DIRECTION('',(0.0,0.0,1.0));
#22=DIRECTION('',(1.0,0.0,0.0));
#25=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION($,(#240,#241,#242,#246,#295,#344,#393,#442,#491,#540,#589,#638,#687,#736,#785,#834,#883,#935,#984,#1033,#1122,#1171,#1260,#1309,#1358,#1427,#1496,#1545,#1594,#1646,#1695,#1744,#1833,#1882,#1971,#2020,#2069,#2138,#2207,#2256,#2305,#2357,#2406,#2455,#2544,#2593,#2682,#2731,#2780,#2849,#2918,#2967,#3016,#3068,#3117,#3166,#3255,#3304,#3393,#3442,#3491,#3560,#3629,#3678,#3727,#3779,#3828,#3877,#3966,#4015,#4104,#4153,#4202,#4271,#4340,#4389,#4438,#4490,#4539,#4588,#4677,#4726,#4815,#4864,#4913,#4982,#5051,#5100,#5149,#5201,#5250,#5299,#5388,#5437,#5526,#5575,#5624,#5693,#5762,#5811,#5860,#5912,#5961,#6010,#6099,#6148,#6237,#6286,#6335,#6404,#6473,#6522,#6571),#11);
#26=PRODUCT_CATEGORY_RELATIONSHIP('','',#27,#28);
#27=PRODUCT_CATEGORY('part','');
#28=PRODUCT_RELATED_PRODUCT_CATEGORY('','',(#8));
#34=PRESENTATION_STYLE_ASSIGNMENT((#35,#40));
#35=SURFACE_STYLE_USAGE(.BOTH.,#36);
#36=SURFACE_SIDE_STYLE('',(#37));
#37=SURFACE_STYLE_FILL_AREA(#38);
#38=FILL_AREA_STYLE('',(#39));
#39=FILL_AREA_STYLE_COLOUR('',#41);
#40=CURVE_STYLE('',#42,POSITIVE_LENGTH_MEASURE(0.1),#41);
#41=COLOUR_RGB('Aluminum',0.725,0.725,0.725);
#42=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#43=PRESENTATION_STYLE_ASSIGNMENT((#44,#49));
#44=SURFACE_STYLE_USAGE(.BOTH.,#45);
#45=SURFACE_SIDE_STYLE('',(#46));
#46=SURFACE_STYLE_FILL_AREA(#47);
#47=FILL_AREA_STYLE('',(#48));
#48=FILL_AREA_STYLE_COLOUR('',#50);
#49=CURVE_STYLE('',#51,POSITIVE_LENGTH_MEASURE(0.1),#50);
#50=COLOUR_RGB('Black',0.196,0.196,0.196);
#51=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#52=PRESENTATION_STYLE_ASSIGNMENT((#53,#58));
#53=SURFACE_STYLE_USAGE(.BOTH.,#54);
#54=SURFACE_SIDE_STYLE('',(#55));
#55=SURFACE_STYLE_FILL_AREA(#56);
#56=FILL_AREA_STYLE('',(#57));
#57=FILL_AREA_STYLE_COLOUR('',#59);
#58=CURVE_STYLE('',#60,POSITIVE_LENGTH_MEASURE(0.1),#59);
#59=COLOUR_RGB('Pin1',0.588,0.588,0.588);
#60=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#61=PRESENTATION_STYLE_ASSIGNMENT((#62,#67));
#62=SURFACE_STYLE_USAGE(.BOTH.,#63);
#63=SURFACE_SIDE_STYLE('',(#64));
#64=SURFACE_STYLE_FILL_AREA(#65);
#65=FILL_AREA_STYLE('',(#66));
#66=FILL_AREA_STYLE_COLOUR('',#68);
#67=CURVE_STYLE('',#69,POSITIVE_LENGTH_MEASURE(0.1),#68);
#68=COLOUR_RGB('HeatTab',0.588,0.588,0.588);
#69=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#70=PRESENTATION_STYLE_ASSIGNMENT((#71,#76));
#71=SURFACE_STYLE_USAGE(.BOTH.,#72);
#72=SURFACE_SIDE_STYLE('',(#73));
#73=SURFACE_STYLE_FILL_AREA(#74);
#74=FILL_AREA_STYLE('',(#75));
#75=FILL_AREA_STYLE_COLOUR('',#77);
#76=CURVE_STYLE('',#78,POSITIVE_LENGTH_MEASURE(0.1),#77);
#77=COLOUR_RGB('Gold',0.843,0.686,0.0);
#78=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#79=PRESENTATION_STYLE_ASSIGNMENT((#80,#85));
#80=SURFACE_STYLE_USAGE(.BOTH.,#81);
#81=SURFACE_SIDE_STYLE('',(#82));
#82=SURFACE_STYLE_FILL_AREA(#83);
#83=FILL_AREA_STYLE('',(#84));
#84=FILL_AREA_STYLE_COLOUR('',#86);
#85=CURVE_STYLE('',#87,POSITIVE_LENGTH_MEASURE(0.1),#86);
#86=COLOUR_RGB('Brown',0.459,0.345,0.176);
#87=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#88=PRESENTATION_STYLE_ASSIGNMENT((#89,#94));
#89=SURFACE_STYLE_USAGE(.BOTH.,#90);
#90=SURFACE_SIDE_STYLE('',(#91));
#91=SURFACE_STYLE_FILL_AREA(#92);
#92=FILL_AREA_STYLE('',(#93));
#93=FILL_AREA_STYLE_COLOUR('',#95);
#94=CURVE_STYLE('',#96,POSITIVE_LENGTH_MEASURE(0.1),#95);
#95=COLOUR_RGB('Tan',0.784,0.686,0.51);
#96=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#97=PRESENTATION_STYLE_ASSIGNMENT((#98,#103));
#98=SURFACE_STYLE_USAGE(.BOTH.,#99);
#99=SURFACE_SIDE_STYLE('',(#100));
#100=SURFACE_STYLE_FILL_AREA(#101);
#101=FILL_AREA_STYLE('',(#102));
#102=FILL_AREA_STYLE_COLOUR('',#104);
#103=CURVE_STYLE('',#105,POSITIVE_LENGTH_MEASURE(0.1),#104);
#104=COLOUR_RGB('Gray',0.431,0.431,0.431);
#105=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#106=PRESENTATION_STYLE_ASSIGNMENT((#107,#112));
#107=SURFACE_STYLE_USAGE(.BOTH.,#108);
#108=SURFACE_SIDE_STYLE('',(#109));
#109=SURFACE_STYLE_FILL_AREA(#110);
#110=FILL_AREA_STYLE('',(#111));
#111=FILL_AREA_STYLE_COLOUR('',#113);
#112=CURVE_STYLE('',#114,POSITIVE_LENGTH_MEASURE(0.1),#113);
#113=COLOUR_RGB('Red',0.6,0.0,0.0);
#114=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#115=PRESENTATION_STYLE_ASSIGNMENT((#116,#121));
#116=SURFACE_STYLE_USAGE(.BOTH.,#117);
#117=SURFACE_SIDE_STYLE('',(#118));
#118=SURFACE_STYLE_FILL_AREA(#119);
#119=FILL_AREA_STYLE('',(#120));
#120=FILL_AREA_STYLE_COLOUR('',#122);
#121=CURVE_STYLE('',#123,POSITIVE_LENGTH_MEASURE(0.1),#122);
#122=COLOUR_RGB('Blue',0.157,0.157,0.588);
#123=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#124=PRESENTATION_STYLE_ASSIGNMENT((#125,#130));
#125=SURFACE_STYLE_USAGE(.BOTH.,#126);
#126=SURFACE_SIDE_STYLE('',(#127));
#127=SURFACE_STYLE_FILL_AREA(#128);
#128=FILL_AREA_STYLE('',(#129));
#129=FILL_AREA_STYLE_COLOUR('',#131);
#130=CURVE_STYLE('',#132,POSITIVE_LENGTH_MEASURE(0.1),#131);
#131=COLOUR_RGB('Maroon',0.294,0.0,0.0);
#132=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#133=PRESENTATION_STYLE_ASSIGNMENT((#134,#139));
#134=SURFACE_STYLE_USAGE(.BOTH.,#135);
#135=SURFACE_SIDE_STYLE('',(#136));
#136=SURFACE_STYLE_FILL_AREA(#137);
#137=FILL_AREA_STYLE('',(#138));
#138=FILL_AREA_STYLE_COLOUR('',#140);
#139=CURVE_STYLE('',#141,POSITIVE_LENGTH_MEASURE(0.1),#140);
#140=COLOUR_RGB('Green',0.0,0.294,0.0);
#141=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#142=PRESENTATION_STYLE_ASSIGNMENT((#143,#148));
#143=SURFACE_STYLE_USAGE(.BOTH.,#144);
#144=SURFACE_SIDE_STYLE('',(#145));
#145=SURFACE_STYLE_FILL_AREA(#146);
#146=FILL_AREA_STYLE('',(#147));
#147=FILL_AREA_STYLE_COLOUR('',#149);
#148=CURVE_STYLE('',#150,POSITIVE_LENGTH_MEASURE(0.1),#149);
#149=COLOUR_RGB('Pin1Wrap',0.98,0.706,0.176);
#150=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#151=PRESENTATION_STYLE_ASSIGNMENT((#152,#157));
#152=SURFACE_STYLE_USAGE(.BOTH.,#153);
#153=SURFACE_SIDE_STYLE('',(#154));
#154=SURFACE_STYLE_FILL_AREA(#155);
#155=FILL_AREA_STYLE('',(#156));
#156=FILL_AREA_STYLE_COLOUR('',#158);
#157=CURVE_STYLE('',#159,POSITIVE_LENGTH_MEASURE(0.1),#158);
#158=COLOUR_RGB('Pin1Rad',0.588,0.588,0.588);
#159=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#160=PRESENTATION_STYLE_ASSIGNMENT((#161,#166));
#161=SURFACE_STYLE_USAGE(.BOTH.,#162);
#162=SURFACE_SIDE_STYLE('',(#163));
#163=SURFACE_STYLE_FILL_AREA(#164);
#164=FILL_AREA_STYLE('',(#165));
#165=FILL_AREA_STYLE_COLOUR('',#167);
#166=CURVE_STYLE('',#168,POSITIVE_LENGTH_MEASURE(0.1),#167);
#167=COLOUR_RGB('Pin1Axial',0.98,0.706,0.176);
#168=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#169=PRESENTATION_STYLE_ASSIGNMENT((#170,#175));
#170=SURFACE_STYLE_USAGE(.BOTH.,#171);
#171=SURFACE_SIDE_STYLE('',(#172));
#172=SURFACE_STYLE_FILL_AREA(#173);
#173=FILL_AREA_STYLE('',(#174));
#174=FILL_AREA_STYLE_COLOUR('',#176);
#175=CURVE_STYLE('',#177,POSITIVE_LENGTH_MEASURE(0.1),#176);
#176=COLOUR_RGB('Pin1Tant',0.459,0.345,0.176);
#177=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#178=PRESENTATION_STYLE_ASSIGNMENT((#179,#184));
#179=SURFACE_STYLE_USAGE(.BOTH.,#180);
#180=SURFACE_SIDE_STYLE('',(#181));
#181=SURFACE_STYLE_FILL_AREA(#182);
#182=FILL_AREA_STYLE('',(#183));
#183=FILL_AREA_STYLE_COLOUR('',#185);
#184=CURVE_STYLE('',#186,POSITIVE_LENGTH_MEASURE(0.1),#185);
#185=COLOUR_RGB('Shroud',0.235,0.235,0.235);
#186=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#188=MANIFOLD_SOLID_BREP($,#189);
#189=CLOSED_SHELL('',(#190,#191,#192));
#190=ADVANCED_FACE($,(#196,#195),#213,.T.);
#191=ADVANCED_FACE($,(#197),#193,.F.);
#192=ADVANCED_FACE($,(#198),#194,.T.);
#193=PLANE($,#218);
#194=PLANE($,#219);
#195=FACE_BOUND($,#200,.T.);
#196=FACE_OUTER_BOUND($,#199,.T.);
#197=FACE_OUTER_BOUND($,#201,.T.);
#198=FACE_OUTER_BOUND($,#202,.T.);
#199=EDGE_LOOP($,(#209));
#200=EDGE_LOOP($,(#210));
#201=EDGE_LOOP($,(#211));
#202=EDGE_LOOP($,(#212));
#203=CIRCLE($,#216,0.1651);
#204=CIRCLE($,#217,0.1651);
#205=VERTEX_POINT('',#234);
#206=VERTEX_POINT('',#236);
#207=EDGE_CURVE($,#205,#205,#203,.T.);
#208=EDGE_CURVE($,#206,#206,#204,.T.);
#209=ORIENTED_EDGE($,*,*,#207,.F.);
#210=ORIENTED_EDGE($,*,*,#208,.F.);
#211=ORIENTED_EDGE($,*,*,#208,.T.);
#212=ORIENTED_EDGE($,*,*,#207,.T.);
#213=CYLINDRICAL_SURFACE($,#215,0.1651);
#214=AXIS2_PLACEMENT_3D('placement',#232,#220,#221);
#215=AXIS2_PLACEMENT_3D($,#233,#222,#223);
#216=AXIS2_PLACEMENT_3D($,#235,#224,#225);
#217=AXIS2_PLACEMENT_3D($,#237,#226,#227);
#218=AXIS2_PLACEMENT_3D($,#238,#228,#229);
#219=AXIS2_PLACEMENT_3D($,#239,#230,#231);
#220=DIRECTION('axis',(0.,1.,0.));
#221=DIRECTION('refdir',(1.,0.,0.));
#222=DIRECTION('',(0.,0.,1.));
#223=DIRECTION('',(1.,0.,0.));
#224=DIRECTION('',(0.,0.,1.));
#225=DIRECTION('',(1.,0.,0.));
#226=DIRECTION('',(0.,0.,-1.));
#227=DIRECTION('',(1.,0.,0.));
#228=DIRECTION('',(0.,0.,1.));
#229=DIRECTION('',(0.,1.,0.));
#230=DIRECTION('',(0.,0.,1.));
#231=DIRECTION('',(0.,1.,0.));
#232=CARTESIAN_POINT('',(0.,0.,0.));
#233=CARTESIAN_POINT('',(2.208,0.0,5.207));
#234=CARTESIAN_POINT('',(2.043,0.0,5.21));
#235=CARTESIAN_POINT('',(2.208,0.0,5.21));
#236=CARTESIAN_POINT('',(2.043,0.0,5.207));
#237=CARTESIAN_POINT('',(2.208,0.0,5.207));
#238=CARTESIAN_POINT('',(2.208,0.0,5.207));
#239=CARTESIAN_POINT('',(2.208,0.0,5.21));
#240=STYLED_ITEM('color',(#52),#190);
#241=STYLED_ITEM('color',(#52),#191);
#242=STYLED_ITEM('color',(#52),#192);
#244=MANIFOLD_SOLID_BREP($,#245);
#245=CLOSED_SHELL('',(#247,#296,#345,#394,#443,#492,#541,#590,#639,#688,#737,#786,#835,#884));
#246=STYLED_ITEM('',(#43),#247);
#247=ADVANCED_FACE('',(#253),#248,.T.);
#248=PLANE('',#249);
#249=AXIS2_PLACEMENT_3D('',#250,#251,#252);
#250=CARTESIAN_POINT('',(1.321,-9.144,5.207));
#251=DIRECTION('',(0.0,0.0,1.0));
#252=DIRECTION('',(0.,1.,0.));
#253=FACE_OUTER_BOUND('',#254,.T.);
#254=EDGE_LOOP('',(#255,#265,#275,#285));
#258=CARTESIAN_POINT('',(6.553,-9.144,5.207));
#257=VERTEX_POINT('',#258);
#260=CARTESIAN_POINT('',(1.321,-9.144,5.207));
#259=VERTEX_POINT('',#260);
#256=EDGE_CURVE('',#257,#259,#261,.T.);
#261=LINE('',#258,#263);
#263=VECTOR('',#264,5.2324);
#264=DIRECTION('',(-1.0,0.0,0.0));
#255=ORIENTED_EDGE('',*,*,#256,.F.);
#268=CARTESIAN_POINT('',(6.553,1.524,5.207));
#267=VERTEX_POINT('',#268);
#266=EDGE_CURVE('',#267,#257,#271,.T.);
#271=LINE('',#268,#273);
#273=VECTOR('',#274,10.668);
#274=DIRECTION('',(0.0,-1.0,0.0));
#265=ORIENTED_EDGE('',*,*,#266,.F.);
#278=CARTESIAN_POINT('',(1.321,1.524,5.207));
#277=VERTEX_POINT('',#278);
#276=EDGE_CURVE('',#277,#267,#281,.T.);
#281=LINE('',#278,#283);
#283=VECTOR('',#284,5.2324);
#284=DIRECTION('',(1.0,0.0,0.0));
#275=ORIENTED_EDGE('',*,*,#276,.F.);
#286=EDGE_CURVE('',#259,#277,#291,.T.);
#291=LINE('',#260,#293);
#293=VECTOR('',#294,10.668);
#294=DIRECTION('',(0.0,1.0,0.0));
#285=ORIENTED_EDGE('',*,*,#286,.F.);
#295=STYLED_ITEM('',(#43),#296);
#296=ADVANCED_FACE('',(#302),#297,.T.);
#297=PLANE('',#298);
#298=AXIS2_PLACEMENT_3D('',#299,#300,#301);
#299=CARTESIAN_POINT('',(0.635,-9.271,3.254));
#300=DIRECTION('',(0.0,-0.998,0.065));
#301=DIRECTION('',(0.,0.,1.));
#302=FACE_OUTER_BOUND('',#303,.T.);
#303=EDGE_LOOP('',(#304,#314,#324,#334));
#307=CARTESIAN_POINT('',(7.239,-9.271,3.254));
#306=VERTEX_POINT('',#307);
#309=CARTESIAN_POINT('',(0.635,-9.271,3.254));
#308=VERTEX_POINT('',#309);
#305=EDGE_CURVE('',#306,#308,#310,.T.);
#310=LINE('',#307,#312);
#312=VECTOR('',#313,6.604);
#313=DIRECTION('',(-1.0,0.0,0.0));
#304=ORIENTED_EDGE('',*,*,#305,.F.);
#315=EDGE_CURVE('',#257,#306,#320,.T.);
#320=LINE('',#258,#322);
#322=VECTOR('',#323,2.07404803888435);
#323=DIRECTION('',(0.331,-0.061,-0.942));
#314=ORIENTED_EDGE('',*,*,#315,.F.);
#324=ORIENTED_EDGE('',*,*,#256,.T.);
#335=EDGE_CURVE('',#308,#259,#340,.T.);
#340=LINE('',#309,#342);
#342=VECTOR('',#343,2.07404803888435);
#343=DIRECTION('',(0.331,0.061,0.942));
#334=ORIENTED_EDGE('',*,*,#335,.F.);
#344=STYLED_ITEM('',(#43),#345);
#345=ADVANCED_FACE('',(#351),#346,.T.);
#346=PLANE('',#347);
#347=AXIS2_PLACEMENT_3D('',#348,#349,#350);
#348=CARTESIAN_POINT('',(7.239,-9.271,3.254));
#349=DIRECTION('',(0.944,0.0,0.331));
#350=DIRECTION('',(0.,0.,1.));
#351=FACE_OUTER_BOUND('',#352,.T.);
#352=EDGE_LOOP('',(#353,#363,#373,#383));
#356=CARTESIAN_POINT('',(7.239,1.651,3.254));
#355=VERTEX_POINT('',#356);
#354=EDGE_CURVE('',#355,#306,#359,.T.);
#359=LINE('',#356,#361);
#361=VECTOR('',#362,10.922);
#362=DIRECTION('',(0.0,-1.0,0.0));
#353=ORIENTED_EDGE('',*,*,#354,.F.);
#364=EDGE_CURVE('',#267,#355,#369,.T.);
#369=LINE('',#268,#371);
#371=VECTOR('',#372,2.07404803888435);
#372=DIRECTION('',(0.331,0.061,-0.942));
#363=ORIENTED_EDGE('',*,*,#364,.F.);
#373=ORIENTED_EDGE('',*,*,#266,.T.);
#383=ORIENTED_EDGE('',*,*,#315,.T.);
#393=STYLED_ITEM('',(#43),#394);
#394=ADVANCED_FACE('',(#400),#395,.T.);
#395=PLANE('',#396);
#396=AXIS2_PLACEMENT_3D('',#397,#398,#399);
#397=CARTESIAN_POINT('',(7.239,1.651,3.254));
#398=DIRECTION('',(0.0,0.998,0.065));
#399=DIRECTION('',(0.,0.,1.));
#400=FACE_OUTER_BOUND('',#401,.T.);
#401=EDGE_LOOP('',(#402,#412,#422,#432));
#405=CARTESIAN_POINT('',(0.635,1.651,3.254));
#404=VERTEX_POINT('',#405);
#403=EDGE_CURVE('',#404,#355,#408,.T.);
#408=LINE('',#405,#410);
#410=VECTOR('',#411,6.604);
#411=DIRECTION('',(1.0,0.0,0.0));
#402=ORIENTED_EDGE('',*,*,#403,.F.);
#413=EDGE_CURVE('',#277,#404,#418,.T.);
#418=LINE('',#278,#420);
#420=VECTOR('',#421,2.07404803888435);
#421=DIRECTION('',(-0.331,0.061,-0.942));
#412=ORIENTED_EDGE('',*,*,#413,.F.);
#422=ORIENTED_EDGE('',*,*,#276,.T.);
#432=ORIENTED_EDGE('',*,*,#364,.T.);
#442=STYLED_ITEM('',(#43),#443);
#443=ADVANCED_FACE('',(#449),#444,.T.);
#444=PLANE('',#445);
#445=AXIS2_PLACEMENT_3D('',#446,#447,#448);
#446=CARTESIAN_POINT('',(0.635,1.651,3.254));
#447=DIRECTION('',(-0.944,0.0,0.331));
#448=DIRECTION('',(0.,0.,1.));
#449=FACE_OUTER_BOUND('',#450,.T.);
#450=EDGE_LOOP('',(#451,#461,#471,#481));
#452=EDGE_CURVE('',#308,#404,#457,.T.);
#457=LINE('',#309,#459);
#459=VECTOR('',#460,10.922);
#460=DIRECTION('',(0.0,1.0,0.0));
#451=ORIENTED_EDGE('',*,*,#452,.F.);
#461=ORIENTED_EDGE('',*,*,#335,.T.);
#471=ORIENTED_EDGE('',*,*,#286,.T.);
#481=ORIENTED_EDGE('',*,*,#413,.T.);
#491=STYLED_ITEM('',(#43),#492);
#492=ADVANCED_FACE('',(#498),#493,.T.);
#493=PLANE('',#494);
#494=AXIS2_PLACEMENT_3D('',#495,#496,#497);
#495=CARTESIAN_POINT('',(0.635,-9.271,2.588));
#496=DIRECTION('',(0.0,-1.0,0.0));
#497=DIRECTION('',(0.,0.,1.));
#498=FACE_OUTER_BOUND('',#499,.T.);
#499=EDGE_LOOP('',(#500,#510,#520,#530));
#503=CARTESIAN_POINT('',(7.239,-9.271,2.588));
#502=VERTEX_POINT('',#503);
#505=CARTESIAN_POINT('',(0.635,-9.271,2.588));
#504=VERTEX_POINT('',#505);
#501=EDGE_CURVE('',#502,#504,#506,.T.);
#506=LINE('',#503,#508);
#508=VECTOR('',#509,6.604);
#509=DIRECTION('',(-1.0,0.0,0.0));
#500=ORIENTED_EDGE('',*,*,#501,.F.);
#511=EDGE_CURVE('',#306,#502,#516,.T.);
#516=LINE('',#307,#518);
#518=VECTOR('',#519,0.66548);
#519=DIRECTION('',(0.0,0.0,-1.0));
#510=ORIENTED_EDGE('',*,*,#511,.F.);
#520=ORIENTED_EDGE('',*,*,#305,.T.);
#531=EDGE_CURVE('',#504,#308,#536,.T.);
#536=LINE('',#505,#538);
#538=VECTOR('',#539,0.66548);
#539=DIRECTION('',(0.0,0.0,1.0));
#530=ORIENTED_EDGE('',*,*,#531,.F.);
#540=STYLED_ITEM('',(#43),#541);
#541=ADVANCED_FACE('',(#547),#542,.T.);
#542=PLANE('',#543);
#543=AXIS2_PLACEMENT_3D('',#544,#545,#546);
#544=CARTESIAN_POINT('',(7.239,-9.271,2.588));
#545=DIRECTION('',(1.0,0.0,0.0));
#546=DIRECTION('',(0.,0.,1.));
#547=FACE_OUTER_BOUND('',#548,.T.);
#548=EDGE_LOOP('',(#549,#559,#569,#579));
#552=CARTESIAN_POINT('',(7.239,1.651,2.588));
#551=VERTEX_POINT('',#552);
#550=EDGE_CURVE('',#551,#502,#555,.T.);
#555=LINE('',#552,#557);
#557=VECTOR('',#558,10.922);
#558=DIRECTION('',(0.0,-1.0,0.0));
#549=ORIENTED_EDGE('',*,*,#550,.F.);
#560=EDGE_CURVE('',#355,#551,#565,.T.);
#565=LINE('',#356,#567);
#567=VECTOR('',#568,0.66548);
#568=DIRECTION('',(0.0,0.0,-1.0));
#559=ORIENTED_EDGE('',*,*,#560,.F.);
#569=ORIENTED_EDGE('',*,*,#354,.T.);
#579=ORIENTED_EDGE('',*,*,#511,.T.);
#589=STYLED_ITEM('',(#43),#590);
#590=ADVANCED_FACE('',(#596),#591,.T.);
#591=PLANE('',#592);
#592=AXIS2_PLACEMENT_3D('',#593,#594,#595);
#593=CARTESIAN_POINT('',(7.239,1.651,2.588));
#594=DIRECTION('',(0.0,1.0,0.0));
#595=DIRECTION('',(0.,0.,1.));
#596=FACE_OUTER_BOUND('',#597,.T.);
#597=EDGE_LOOP('',(#598,#608,#618,#628));
#601=CARTESIAN_POINT('',(0.635,1.651,2.588));
#600=VERTEX_POINT('',#601);
#599=EDGE_CURVE('',#600,#551,#604,.T.);
#604=LINE('',#601,#606);
#606=VECTOR('',#607,6.604);
#607=DIRECTION('',(1.0,0.0,0.0));
#598=ORIENTED_EDGE('',*,*,#599,.F.);
#609=EDGE_CURVE('',#404,#600,#614,.T.);
#614=LINE('',#405,#616);
#616=VECTOR('',#617,0.66548);
#617=DIRECTION('',(0.0,0.0,-1.0));
#608=ORIENTED_EDGE('',*,*,#609,.F.);
#618=ORIENTED_EDGE('',*,*,#403,.T.);
#628=ORIENTED_EDGE('',*,*,#560,.T.);
#638=STYLED_ITEM('',(#43),#639);
#639=ADVANCED_FACE('',(#645),#640,.T.);
#640=PLANE('',#641);
#641=AXIS2_PLACEMENT_3D('',#642,#643,#644);
#642=CARTESIAN_POINT('',(0.635,1.651,2.588));
#643=DIRECTION('',(-1.0,0.0,0.0));
#644=DIRECTION('',(0.,0.,1.));
#645=FACE_OUTER_BOUND('',#646,.T.);
#646=EDGE_LOOP('',(#647,#657,#667,#677));
#648=EDGE_CURVE('',#504,#600,#653,.T.);
#653=LINE('',#505,#655);
#655=VECTOR('',#656,10.922);
#656=DIRECTION('',(0.0,1.0,0.0));
#647=ORIENTED_EDGE('',*,*,#648,.F.);
#657=ORIENTED_EDGE('',*,*,#531,.T.);
#667=ORIENTED_EDGE('',*,*,#452,.T.);
#677=ORIENTED_EDGE('',*,*,#609,.T.);
#687=STYLED_ITEM('',(#43),#688);
#688=ADVANCED_FACE('',(#694),#689,.T.);
#689=PLANE('',#690);
#690=AXIS2_PLACEMENT_3D('',#691,#692,#693);
#691=CARTESIAN_POINT('',(0.965,-9.093,0.635));
#692=DIRECTION('',(0.0,-0.996,-0.091));
#693=DIRECTION('',(0.,0.,1.));
#694=FACE_OUTER_BOUND('',#695,.T.);
#695=EDGE_LOOP('',(#696,#706,#716,#726));
#699=CARTESIAN_POINT('',(6.909,-9.093,0.635));
#698=VERTEX_POINT('',#699);
#701=CARTESIAN_POINT('',(0.965,-9.093,0.635));
#700=VERTEX_POINT('',#701);
#697=EDGE_CURVE('',#698,#700,#702,.T.);
#702=LINE('',#699,#704);
#704=VECTOR('',#705,5.9436);
#705=DIRECTION('',(-1.0,0.0,0.0));
#696=ORIENTED_EDGE('',*,*,#697,.F.);
#707=EDGE_CURVE('',#502,#698,#712,.T.);
#712=LINE('',#503,#714);
#714=VECTOR('',#715,1.98893677818074);
#715=DIRECTION('',(-0.166,0.089,-0.982));
#706=ORIENTED_EDGE('',*,*,#707,.F.);
#716=ORIENTED_EDGE('',*,*,#501,.T.);
#727=EDGE_CURVE('',#700,#504,#732,.T.);
#732=LINE('',#701,#734);
#734=VECTOR('',#735,1.98893677818074);
#735=DIRECTION('',(-0.166,-0.089,0.982));
#726=ORIENTED_EDGE('',*,*,#727,.F.);
#736=STYLED_ITEM('',(#43),#737);
#737=ADVANCED_FACE('',(#743),#738,.T.);
#738=PLANE('',#739);
#739=AXIS2_PLACEMENT_3D('',#740,#741,#742);
#740=CARTESIAN_POINT('',(6.909,-9.093,0.635));
#741=DIRECTION('',(0.986,0.0,-0.167));
#742=DIRECTION('',(0.,0.,1.));
#743=FACE_OUTER_BOUND('',#744,.T.);
#744=EDGE_LOOP('',(#745,#755,#765,#775));
#748=CARTESIAN_POINT('',(6.909,1.473,0.635));
#747=VERTEX_POINT('',#748);
#746=EDGE_CURVE('',#747,#698,#751,.T.);
#751=LINE('',#748,#753);
#753=VECTOR('',#754,10.5664);
#754=DIRECTION('',(0.0,-1.0,0.0));
#745=ORIENTED_EDGE('',*,*,#746,.F.);
#756=EDGE_CURVE('',#551,#747,#761,.T.);
#761=LINE('',#552,#763);
#763=VECTOR('',#764,1.98893677818074);
#764=DIRECTION('',(-0.166,-0.089,-0.982));
#755=ORIENTED_EDGE('',*,*,#756,.F.);
#765=ORIENTED_EDGE('',*,*,#550,.T.);
#775=ORIENTED_EDGE('',*,*,#707,.T.);
#785=STYLED_ITEM('',(#43),#786);
#786=ADVANCED_FACE('',(#792),#787,.T.);
#787=PLANE('',#788);
#788=AXIS2_PLACEMENT_3D('',#789,#790,#791);
#789=CARTESIAN_POINT('',(6.909,1.473,0.635));
#790=DIRECTION('',(0.0,0.996,-0.091));
#791=DIRECTION('',(0.,0.,1.));
#792=FACE_OUTER_BOUND('',#793,.T.);
#793=EDGE_LOOP('',(#794,#804,#814,#824));
#797=CARTESIAN_POINT('',(0.965,1.473,0.635));
#796=VERTEX_POINT('',#797);
#795=EDGE_CURVE('',#796,#747,#800,.T.);
#800=LINE('',#797,#802);
#802=VECTOR('',#803,5.9436);
#803=DIRECTION('',(1.0,0.0,0.0));
#794=ORIENTED_EDGE('',*,*,#795,.F.);
#805=EDGE_CURVE('',#600,#796,#810,.T.);
#810=LINE('',#601,#812);
#812=VECTOR('',#813,1.98893677818074);
#813=DIRECTION('',(0.166,-0.089,-0.982));
#804=ORIENTED_EDGE('',*,*,#805,.F.);
#814=ORIENTED_EDGE('',*,*,#599,.T.);
#824=ORIENTED_EDGE('',*,*,#756,.T.);
#834=STYLED_ITEM('',(#43),#835);
#835=ADVANCED_FACE('',(#841),#836,.T.);
#836=PLANE('',#837);
#837=AXIS2_PLACEMENT_3D('',#838,#839,#840);
#838=CARTESIAN_POINT('',(0.965,1.473,0.635));
#839=DIRECTION('',(-0.986,0.0,-0.167));
#840=DIRECTION('',(0.,0.,1.));
#841=FACE_OUTER_BOUND('',#842,.T.);
#842=EDGE_LOOP('',(#843,#853,#863,#873));
#844=EDGE_CURVE('',#700,#796,#849,.T.);
#849=LINE('',#701,#851);
#851=VECTOR('',#852,10.5664);
#852=DIRECTION('',(0.0,1.0,0.0));
#843=ORIENTED_EDGE('',*,*,#844,.F.);
#853=ORIENTED_EDGE('',*,*,#727,.T.);
#863=ORIENTED_EDGE('',*,*,#648,.T.);
#873=ORIENTED_EDGE('',*,*,#805,.T.);
#883=STYLED_ITEM('',(#43),#884);
#884=ADVANCED_FACE('',(#890),#885,.T.);
#885=PLANE('',#886);
#886=AXIS2_PLACEMENT_3D('',#887,#888,#889);
#887=CARTESIAN_POINT('',(0.965,1.473,0.635));
#888=DIRECTION('',(0.0,0.0,-1.0));
#889=DIRECTION('',(0.,1.,0.));
#890=FACE_OUTER_BOUND('',#891,.T.);
#891=EDGE_LOOP('',(#892,#902,#912,#922));
#892=ORIENTED_EDGE('',*,*,#795,.T.);
#902=ORIENTED_EDGE('',*,*,#746,.T.);
#912=ORIENTED_EDGE('',*,*,#697,.T.);
#922=ORIENTED_EDGE('',*,*,#844,.T.);
#933=MANIFOLD_SOLID_BREP($,#934);
#934=CLOSED_SHELL('',(#936,#985,#1034,#1123,#1172,#1261,#1310,#1359,#1428,#1497,#1546,#1595));
#935=STYLED_ITEM('',(#34),#936);
#936=ADVANCED_FACE('',(#942),#937,.T.);
#937=PLANE('',#938);
#938=AXIS2_PLACEMENT_3D('',#939,#940,#941);
#939=CARTESIAN_POINT('',(0.127,0.279,-2.54));
#940=DIRECTION('',(0.0,0.0,-1.0));
#941=DIRECTION('',(0.,1.,0.));
#942=FACE_OUTER_BOUND('',#943,.T.);
#943=EDGE_LOOP('',(#944,#954,#964,#974));
#947=CARTESIAN_POINT('',(0.127,-0.279,-2.54));
#946=VERTEX_POINT('',#947);
#949=CARTESIAN_POINT('',(0.127,0.279,-2.54));
#948=VERTEX_POINT('',#949);
#945=EDGE_CURVE('',#946,#948,#950,.T.);
#950=LINE('',#947,#952);
#952=VECTOR('',#953,0.5588);
#953=DIRECTION('',(0.0,1.0,0.0));
#944=ORIENTED_EDGE('',*,*,#945,.F.);
#957=CARTESIAN_POINT('',(-0.127,-0.279,-2.54));
#956=VERTEX_POINT('',#957);
#955=EDGE_CURVE('',#956,#946,#960,.T.);
#960=LINE('',#957,#962);
#962=VECTOR('',#963,0.254);
#963=DIRECTION('',(1.0,0.0,0.0));
#954=ORIENTED_EDGE('',*,*,#955,.F.);
#967=CARTESIAN_POINT('',(-0.127,0.279,-2.54));
#966=VERTEX_POINT('',#967);
#965=EDGE_CURVE('',#966,#956,#970,.T.);
#970=LINE('',#967,#972);
#972=VECTOR('',#973,0.5588);
#973=DIRECTION('',(0.0,-1.0,0.0));
#964=ORIENTED_EDGE('',*,*,#965,.F.);
#975=EDGE_CURVE('',#948,#966,#980,.T.);
#980=LINE('',#949,#982);
#982=VECTOR('',#983,0.254);
#983=DIRECTION('',(-1.0,0.0,0.0));
#974=ORIENTED_EDGE('',*,*,#975,.F.);
#984=STYLED_ITEM('',(#34),#985);
#985=ADVANCED_FACE('',(#991),#986,.T.);
#986=PLANE('',#987);
#987=AXIS2_PLACEMENT_3D('',#988,#989,#990);
#988=CARTESIAN_POINT('',(0.127,0.279,-2.54));
#989=DIRECTION('',(0.0,1.0,0.0));
#990=DIRECTION('',(0.,0.,1.));
#991=FACE_OUTER_BOUND('',#992,.T.);
#992=EDGE_LOOP('',(#993,#1003,#1013,#1023));
#993=ORIENTED_EDGE('',*,*,#975,.T.);
#1006=CARTESIAN_POINT('',(-0.127,0.279,0.0));
#1005=VERTEX_POINT('',#1006);
#1004=EDGE_CURVE('',#1005,#966,#1009,.T.);
#1009=LINE('',#1006,#1011);
#1011=VECTOR('',#1012,2.54);
#1012=DIRECTION('',(0.0,0.0,-1.0));
#1003=ORIENTED_EDGE('',*,*,#1004,.F.);
#1016=CARTESIAN_POINT('',(0.127,0.279,0.0));
#1015=VERTEX_POINT('',#1016);
#1014=EDGE_CURVE('',#1015,#1005,#1019,.T.);
#1019=LINE('',#1016,#1021);
#1021=VECTOR('',#1022,0.254);
#1022=DIRECTION('',(-1.0,0.0,0.0));
#1013=ORIENTED_EDGE('',*,*,#1014,.F.);
#1024=EDGE_CURVE('',#948,#1015,#1029,.T.);
#1029=LINE('',#949,#1031);
#1031=VECTOR('',#1032,2.54);
#1032=DIRECTION('',(0.0,0.0,1.0));
#1023=ORIENTED_EDGE('',*,*,#1024,.F.);
#1033=STYLED_ITEM('',(#34),#1034);
#1034=ADVANCED_FACE('',(#1040),#1035,.T.);
#1035=PLANE('',#1036);
#1036=AXIS2_PLACEMENT_3D('',#1037,#1038,#1039);
#1037=CARTESIAN_POINT('',(-0.127,0.279,-2.54));
#1038=DIRECTION('',(-1.0,0.0,0.0));
#1039=DIRECTION('',(0.,0.,1.));
#1040=FACE_OUTER_BOUND('',#1041,.T.);
#1041=EDGE_LOOP('',(#1042,#1052,#1062,#1072,#1082,#1092,#1102,#1112));
#1042=ORIENTED_EDGE('',*,*,#965,.T.);
#1055=CARTESIAN_POINT('',(-0.127,-0.279,0.0));
#1054=VERTEX_POINT('',#1055);
#1053=EDGE_CURVE('',#1054,#956,#1058,.T.);
#1058=LINE('',#1055,#1060);
#1060=VECTOR('',#1061,2.54);
#1061=DIRECTION('',(0.0,0.0,-1.0));
#1052=ORIENTED_EDGE('',*,*,#1053,.F.);
#1065=CARTESIAN_POINT('',(-0.127,-0.66,0.0));
#1064=VERTEX_POINT('',#1065);
#1063=EDGE_CURVE('',#1064,#1054,#1068,.T.);
#1068=LINE('',#1065,#1070);
#1070=VECTOR('',#1071,0.381);
#1071=DIRECTION('',(0.0,1.0,0.0));
#1062=ORIENTED_EDGE('',*,*,#1063,.F.);
#1075=CARTESIAN_POINT('',(-0.127,-0.66,3.048));
#1074=VERTEX_POINT('',#1075);
#1073=EDGE_CURVE('',#1074,#1064,#1078,.T.);
#1078=LINE('',#1075,#1080);
#1080=VECTOR('',#1081,3.048);
#1081=DIRECTION('',(0.0,0.0,-1.0));
#1072=ORIENTED_EDGE('',*,*,#1073,.F.);
#1085=CARTESIAN_POINT('',(-0.127,0.66,3.048));
#1084=VERTEX_POINT('',#1085);
#1083=EDGE_CURVE('',#1084,#1074,#1088,.T.);
#1088=LINE('',#1085,#1090);
#1090=VECTOR('',#1091,1.3208);
#1091=DIRECTION('',(0.0,-1.0,0.0));
#1082=ORIENTED_EDGE('',*,*,#1083,.F.);
#1095=CARTESIAN_POINT('',(-0.127,0.66,0.0));
#1094=VERTEX_POINT('',#1095);
#1093=EDGE_CURVE('',#1094,#1084,#1098,.T.);
#1098=LINE('',#1095,#1100);
#1100=VECTOR('',#1101,3.048);
#1101=DIRECTION('',(0.0,0.0,1.0));
#1092=ORIENTED_EDGE('',*,*,#1093,.F.);
#1103=EDGE_CURVE('',#1005,#1094,#1108,.T.);
#1108=LINE('',#1006,#1110);
#1110=VECTOR('',#1111,0.381);
#1111=DIRECTION('',(0.0,1.0,0.0));
#1102=ORIENTED_EDGE('',*,*,#1103,.F.);
#1112=ORIENTED_EDGE('',*,*,#1004,.T.);
#1122=STYLED_ITEM('',(#34),#1123);
#1123=ADVANCED_FACE('',(#1129),#1124,.T.);
#1124=PLANE('',#1125);
#1125=AXIS2_PLACEMENT_3D('',#1126,#1127,#1128);
#1126=CARTESIAN_POINT('',(-0.127,-0.279,-2.54));
#1127=DIRECTION('',(0.0,-1.0,0.0));
#1128=DIRECTION('',(0.,0.,1.));
#1129=FACE_OUTER_BOUND('',#1130,.T.);
#1130=EDGE_LOOP('',(#1131,#1141,#1151,#1161));
#1131=ORIENTED_EDGE('',*,*,#955,.T.);
#1144=CARTESIAN_POINT('',(0.127,-0.279,0.0));
#1143=VERTEX_POINT('',#1144);
#1142=EDGE_CURVE('',#1143,#946,#1147,.T.);
#1147=LINE('',#1144,#1149);
#1149=VECTOR('',#1150,2.54);
#1150=DIRECTION('',(0.0,0.0,-1.0));
#1141=ORIENTED_EDGE('',*,*,#1142,.F.);
#1152=EDGE_CURVE('',#1054,#1143,#1157,.T.);
#1157=LINE('',#1055,#1159);
#1159=VECTOR('',#1160,0.254);
#1160=DIRECTION('',(1.0,0.0,0.0));
#1151=ORIENTED_EDGE('',*,*,#1152,.F.);
#1161=ORIENTED_EDGE('',*,*,#1053,.T.);
#1171=STYLED_ITEM('',(#34),#1172);
#1172=ADVANCED_FACE('',(#1178),#1173,.T.);
#1173=PLANE('',#1174);
#1174=AXIS2_PLACEMENT_3D('',#1175,#1176,#1177);
#1175=CARTESIAN_POINT('',(0.127,-0.279,-2.54));
#1176=DIRECTION('',(1.0,0.0,0.0));
#1177=DIRECTION('',(0.,0.,1.));
#1178=FACE_OUTER_BOUND('',#1179,.T.);
#1179=EDGE_LOOP('',(#1180,#1190,#1200,#1210,#1220,#1230,#1240,#1250));
#1180=ORIENTED_EDGE('',*,*,#945,.T.);
#1190=ORIENTED_EDGE('',*,*,#1024,.T.);
#1203=CARTESIAN_POINT('',(0.127,0.66,0.0));
#1202=VERTEX_POINT('',#1203);
#1201=EDGE_CURVE('',#1202,#1015,#1206,.T.);
#1206=LINE('',#1203,#1208);
#1208=VECTOR('',#1209,0.381);
#1209=DIRECTION('',(0.0,-1.0,0.0));
#1200=ORIENTED_EDGE('',*,*,#1201,.F.);
#1213=CARTESIAN_POINT('',(0.127,0.66,2.794));
#1212=VERTEX_POINT('',#1213);
#1211=EDGE_CURVE('',#1212,#1202,#1216,.T.);
#1216=LINE('',#1213,#1218);
#1218=VECTOR('',#1219,2.794);
#1219=DIRECTION('',(0.0,0.0,-1.0));
#1210=ORIENTED_EDGE('',*,*,#1211,.F.);
#1223=CARTESIAN_POINT('',(0.127,-0.66,2.794));
#1222=VERTEX_POINT('',#1223);
#1221=EDGE_CURVE('',#1222,#1212,#1226,.T.);
#1226=LINE('',#1223,#1228);
#1228=VECTOR('',#1229,1.3208);
#1229=DIRECTION('',(0.0,1.0,0.0));
#1220=ORIENTED_EDGE('',*,*,#1221,.F.);
#1233=CARTESIAN_POINT('',(0.127,-0.66,0.0));
#1232=VERTEX_POINT('',#1233);
#1231=EDGE_CURVE('',#1232,#1222,#1236,.T.);
#1236=LINE('',#1233,#1238);
#1238=VECTOR('',#1239,2.794);
#1239=DIRECTION('',(0.0,0.0,1.0));
#1230=ORIENTED_EDGE('',*,*,#1231,.F.);
#1241=EDGE_CURVE('',#1143,#1232,#1246,.T.);
#1246=LINE('',#1144,#1248);
#1248=VECTOR('',#1249,0.381);
#1249=DIRECTION('',(0.0,-1.0,0.0));
#1240=ORIENTED_EDGE('',*,*,#1241,.F.);
#1250=ORIENTED_EDGE('',*,*,#1142,.T.);
#1260=STYLED_ITEM('',(#34),#1261);
#1261=ADVANCED_FACE('',(#1267),#1262,.T.);
#1262=PLANE('',#1263);
#1263=AXIS2_PLACEMENT_3D('',#1264,#1265,#1266);
#1264=CARTESIAN_POINT('',(0.127,0.279,0.0));
#1265=DIRECTION('',(0.0,0.0,-1.0));
#1266=DIRECTION('',(0.,1.,0.));
#1267=FACE_OUTER_BOUND('',#1268,.T.);
#1268=EDGE_LOOP('',(#1269,#1279,#1289,#1299));
#1269=ORIENTED_EDGE('',*,*,#1014,.T.);
#1279=ORIENTED_EDGE('',*,*,#1103,.T.);
#1290=EDGE_CURVE('',#1202,#1094,#1295,.T.);
#1295=LINE('',#1203,#1297);
#1297=VECTOR('',#1298,0.254);
#1298=DIRECTION('',(-1.0,0.0,0.0));
#1289=ORIENTED_EDGE('',*,*,#1290,.F.);
#1299=ORIENTED_EDGE('',*,*,#1201,.T.);
#1309=STYLED_ITEM('',(#34),#1310);
#1310=ADVANCED_FACE('',(#1316),#1311,.T.);
#1311=PLANE('',#1312);
#1312=AXIS2_PLACEMENT_3D('',#1313,#1314,#1315);
#1313=CARTESIAN_POINT('',(0.127,-0.66,0.0));
#1314=DIRECTION('',(0.0,0.0,-1.0));
#1315=DIRECTION('',(0.,1.,0.));
#1316=FACE_OUTER_BOUND('',#1317,.T.);
#1317=EDGE_LOOP('',(#1318,#1328,#1338,#1348));
#1319=EDGE_CURVE('',#1064,#1232,#1324,.T.);
#1324=LINE('',#1065,#1326);
#1326=VECTOR('',#1327,0.254);
#1327=DIRECTION('',(1.0,0.0,0.0));
#1318=ORIENTED_EDGE('',*,*,#1319,.F.);
#1328=ORIENTED_EDGE('',*,*,#1063,.T.);
#1338=ORIENTED_EDGE('',*,*,#1152,.T.);
#1348=ORIENTED_EDGE('',*,*,#1241,.T.);
#1358=STYLED_ITEM('',(#34),#1359);
#1359=ADVANCED_FACE('',(#1365),#1360,.T.);
#1360=PLANE('',#1361);
#1361=AXIS2_PLACEMENT_3D('',#1362,#1363,#1364);
#1362=CARTESIAN_POINT('',(0.127,0.66,0.0));
#1363=DIRECTION('',(0.0,1.0,0.0));
#1364=DIRECTION('',(0.,0.,1.));
#1365=FACE_OUTER_BOUND('',#1366,.T.);
#1366=EDGE_LOOP('',(#1367,#1377,#1387,#1397,#1407,#1417));
#1367=ORIENTED_EDGE('',*,*,#1290,.T.);
#1377=ORIENTED_EDGE('',*,*,#1093,.T.);
#1390=CARTESIAN_POINT('',(0.635,0.66,3.048));
#1389=VERTEX_POINT('',#1390);
#1388=EDGE_CURVE('',#1389,#1084,#1393,.T.);
#1393=LINE('',#1390,#1395);
#1395=VECTOR('',#1396,0.762);
#1396=DIRECTION('',(-1.0,0.0,0.0));
#1387=ORIENTED_EDGE('',*,*,#1388,.F.);
#1400=CARTESIAN_POINT('',(0.635,0.66,2.794));
#1399=VERTEX_POINT('',#1400);
#1398=EDGE_CURVE('',#1399,#1389,#1403,.T.);
#1403=LINE('',#1400,#1405);
#1405=VECTOR('',#1406,0.254);
#1406=DIRECTION('',(0.0,0.0,1.0));
#1397=ORIENTED_EDGE('',*,*,#1398,.F.);
#1408=EDGE_CURVE('',#1212,#1399,#1413,.T.);
#1413=LINE('',#1213,#1415);
#1415=VECTOR('',#1416,0.508);
#1416=DIRECTION('',(1.0,0.0,0.0));
#1407=ORIENTED_EDGE('',*,*,#1408,.F.);
#1417=ORIENTED_EDGE('',*,*,#1211,.T.);
#1427=STYLED_ITEM('',(#34),#1428);
#1428=ADVANCED_FACE('',(#1434),#1429,.T.);
#1429=PLANE('',#1430);
#1430=AXIS2_PLACEMENT_3D('',#1431,#1432,#1433);
#1431=CARTESIAN_POINT('',(-0.127,-0.66,0.0));
#1432=DIRECTION('',(0.0,-1.0,0.0));
#1433=DIRECTION('',(0.,0.,1.));
#1434=FACE_OUTER_BOUND('',#1435,.T.);
#1435=EDGE_LOOP('',(#1436,#1446,#1456,#1466,#1476,#1486));
#1436=ORIENTED_EDGE('',*,*,#1319,.T.);
#1446=ORIENTED_EDGE('',*,*,#1231,.T.);
#1459=CARTESIAN_POINT('',(0.635,-0.66,2.794));
#1458=VERTEX_POINT('',#1459);
#1457=EDGE_CURVE('',#1458,#1222,#1462,.T.);
#1462=LINE('',#1459,#1464);
#1464=VECTOR('',#1465,0.508);
#1465=DIRECTION('',(-1.0,0.0,0.0));
#1456=ORIENTED_EDGE('',*,*,#1457,.F.);
#1469=CARTESIAN_POINT('',(0.635,-0.66,3.048));
#1468=VERTEX_POINT('',#1469);
#1467=EDGE_CURVE('',#1468,#1458,#1472,.T.);
#1472=LINE('',#1469,#1474);
#1474=VECTOR('',#1475,0.254);
#1475=DIRECTION('',(0.0,0.0,-1.0));
#1466=ORIENTED_EDGE('',*,*,#1467,.F.);
#1477=EDGE_CURVE('',#1074,#1468,#1482,.T.);
#1482=LINE('',#1075,#1484);
#1484=VECTOR('',#1485,0.762);
#1485=DIRECTION('',(1.0,0.0,0.0));
#1476=ORIENTED_EDGE('',*,*,#1477,.F.);
#1486=ORIENTED_EDGE('',*,*,#1073,.T.);
#1496=STYLED_ITEM('',(#34),#1497);
#1497=ADVANCED_FACE('',(#1503),#1498,.T.);
#1498=PLANE('',#1499);
#1499=AXIS2_PLACEMENT_3D('',#1500,#1501,#1502);
#1500=CARTESIAN_POINT('',(0.635,-0.66,2.794));
#1501=DIRECTION('',(0.0,0.0,-1.0));
#1502=DIRECTION('',(0.,1.,0.));
#1503=FACE_OUTER_BOUND('',#1504,.T.);
#1504=EDGE_LOOP('',(#1505,#1515,#1525,#1535));
#1505=ORIENTED_EDGE('',*,*,#1457,.T.);
#1515=ORIENTED_EDGE('',*,*,#1221,.T.);
#1525=ORIENTED_EDGE('',*,*,#1408,.T.);
#1536=EDGE_CURVE('',#1458,#1399,#1541,.T.);
#1541=LINE('',#1459,#1543);
#1543=VECTOR('',#1544,1.3208);
#1544=DIRECTION('',(0.0,1.0,0.0));
#1535=ORIENTED_EDGE('',*,*,#1536,.F.);
#1545=STYLED_ITEM('',(#34),#1546);
#1546=ADVANCED_FACE('',(#1552),#1547,.T.);
#1547=PLANE('',#1548);
#1548=AXIS2_PLACEMENT_3D('',#1549,#1550,#1551);
#1549=CARTESIAN_POINT('',(0.635,-0.66,2.794));
#1550=DIRECTION('',(1.0,0.0,0.0));
#1551=DIRECTION('',(0.,0.,1.));
#1552=FACE_OUTER_BOUND('',#1553,.T.);
#1553=EDGE_LOOP('',(#1554,#1564,#1574,#1584));
#1554=ORIENTED_EDGE('',*,*,#1536,.T.);
#1564=ORIENTED_EDGE('',*,*,#1398,.T.);
#1575=EDGE_CURVE('',#1468,#1389,#1580,.T.);
#1580=LINE('',#1469,#1582);
#1582=VECTOR('',#1583,1.3208);
#1583=DIRECTION('',(0.0,1.0,0.0));
#1574=ORIENTED_EDGE('',*,*,#1575,.F.);
#1584=ORIENTED_EDGE('',*,*,#1467,.T.);
#1594=STYLED_ITEM('',(#34),#1595);
#1595=ADVANCED_FACE('',(#1601),#1596,.T.);
#1596=PLANE('',#1597);
#1597=AXIS2_PLACEMENT_3D('',#1598,#1599,#1600);
#1598=CARTESIAN_POINT('',(0.635,0.66,3.048));
#1599=DIRECTION('',(0.0,0.0,1.0));
#1600=DIRECTION('',(0.,1.,0.));
#1601=FACE_OUTER_BOUND('',#1602,.T.);
#1602=EDGE_LOOP('',(#1603,#1613,#1623,#1633));
#1603=ORIENTED_EDGE('',*,*,#1388,.T.);
#1613=ORIENTED_EDGE('',*,*,#1083,.T.);
#1623=ORIENTED_EDGE('',*,*,#1477,.T.);
#1633=ORIENTED_EDGE('',*,*,#1575,.T.);
#1644=MANIFOLD_SOLID_BREP($,#1645);
#1645=CLOSED_SHELL('',(#1647,#1696,#1745,#1834,#1883,#1972,#2021,#2070,#2139,#2208,#2257,#2306));
#1646=STYLED_ITEM('',(#34),#1647);
#1647=ADVANCED_FACE('',(#1653),#1648,.T.);
#1648=PLANE('',#1649);
#1649=AXIS2_PLACEMENT_3D('',#1650,#1651,#1652);
#1650=CARTESIAN_POINT('',(0.127,-2.261,-2.54));
#1651=DIRECTION('',(0.0,0.0,-1.0));
#1652=DIRECTION('',(0.,1.,0.));
#1653=FACE_OUTER_BOUND('',#1654,.T.);
#1654=EDGE_LOOP('',(#1655,#1665,#1675,#1685));
#1658=CARTESIAN_POINT('',(0.127,-2.819,-2.54));
#1657=VERTEX_POINT('',#1658);
#1660=CARTESIAN_POINT('',(0.127,-2.261,-2.54));
#1659=VERTEX_POINT('',#1660);
#1656=EDGE_CURVE('',#1657,#1659,#1661,.T.);
#1661=LINE('',#1658,#1663);
#1663=VECTOR('',#1664,0.5588);
#1664=DIRECTION('',(0.0,1.0,0.0));
#1655=ORIENTED_EDGE('',*,*,#1656,.F.);
#1668=CARTESIAN_POINT('',(-0.127,-2.819,-2.54));
#1667=VERTEX_POINT('',#1668);
#1666=EDGE_CURVE('',#1667,#1657,#1671,.T.);
#1671=LINE('',#1668,#1673);
#1673=VECTOR('',#1674,0.254);
#1674=DIRECTION('',(1.0,0.0,0.0));
#1665=ORIENTED_EDGE('',*,*,#1666,.F.);
#1678=CARTESIAN_POINT('',(-0.127,-2.261,-2.54));
#1677=VERTEX_POINT('',#1678);
#1676=EDGE_CURVE('',#1677,#1667,#1681,.T.);
#1681=LINE('',#1678,#1683);
#1683=VECTOR('',#1684,0.5588);
#1684=DIRECTION('',(0.0,-1.0,0.0));
#1675=ORIENTED_EDGE('',*,*,#1676,.F.);
#1686=EDGE_CURVE('',#1659,#1677,#1691,.T.);
#1691=LINE('',#1660,#1693);
#1693=VECTOR('',#1694,0.254);
#1694=DIRECTION('',(-1.0,0.0,0.0));
#1685=ORIENTED_EDGE('',*,*,#1686,.F.);
#1695=STYLED_ITEM('',(#34),#1696);
#1696=ADVANCED_FACE('',(#1702),#1697,.T.);
#1697=PLANE('',#1698);
#1698=AXIS2_PLACEMENT_3D('',#1699,#1700,#1701);
#1699=CARTESIAN_POINT('',(0.127,-2.261,-2.54));
#1700=DIRECTION('',(0.0,1.0,0.0));
#1701=DIRECTION('',(0.,0.,1.));
#1702=FACE_OUTER_BOUND('',#1703,.T.);
#1703=EDGE_LOOP('',(#1704,#1714,#1724,#1734));
#1704=ORIENTED_EDGE('',*,*,#1686,.T.);
#1717=CARTESIAN_POINT('',(-0.127,-2.261,0.0));
#1716=VERTEX_POINT('',#1717);
#1715=EDGE_CURVE('',#1716,#1677,#1720,.T.);
#1720=LINE('',#1717,#1722);
#1722=VECTOR('',#1723,2.54);
#1723=DIRECTION('',(0.0,0.0,-1.0));
#1714=ORIENTED_EDGE('',*,*,#1715,.F.);
#1727=CARTESIAN_POINT('',(0.127,-2.261,0.0));
#1726=VERTEX_POINT('',#1727);
#1725=EDGE_CURVE('',#1726,#1716,#1730,.T.);
#1730=LINE('',#1727,#1732);
#1732=VECTOR('',#1733,0.254);
#1733=DIRECTION('',(-1.0,0.0,0.0));
#1724=ORIENTED_EDGE('',*,*,#1725,.F.);
#1735=EDGE_CURVE('',#1659,#1726,#1740,.T.);
#1740=LINE('',#1660,#1742);
#1742=VECTOR('',#1743,2.54);
#1743=DIRECTION('',(0.0,0.0,1.0));
#1734=ORIENTED_EDGE('',*,*,#1735,.F.);
#1744=STYLED_ITEM('',(#34),#1745);
#1745=ADVANCED_FACE('',(#1751),#1746,.T.);
#1746=PLANE('',#1747);
#1747=AXIS2_PLACEMENT_3D('',#1748,#1749,#1750);
#1748=CARTESIAN_POINT('',(-0.127,-2.261,-2.54));
#1749=DIRECTION('',(-1.0,0.0,0.0));
#1750=DIRECTION('',(0.,0.,1.));
#1751=FACE_OUTER_BOUND('',#1752,.T.);
#1752=EDGE_LOOP('',(#1753,#1763,#1773,#1783,#1793,#1803,#1813,#1823));
#1753=ORIENTED_EDGE('',*,*,#1676,.T.);
#1766=CARTESIAN_POINT('',(-0.127,-2.819,0.0));
#1765=VERTEX_POINT('',#1766);
#1764=EDGE_CURVE('',#1765,#1667,#1769,.T.);
#1769=LINE('',#1766,#1771);
#1771=VECTOR('',#1772,2.54);
#1772=DIRECTION('',(0.0,0.0,-1.0));
#1763=ORIENTED_EDGE('',*,*,#1764,.F.);
#1776=CARTESIAN_POINT('',(-0.127,-3.2,0.0));
#1775=VERTEX_POINT('',#1776);
#1774=EDGE_CURVE('',#1775,#1765,#1779,.T.);
#1779=LINE('',#1776,#1781);
#1781=VECTOR('',#1782,0.381);
#1782=DIRECTION('',(0.0,1.0,0.0));
#1773=ORIENTED_EDGE('',*,*,#1774,.F.);
#1786=CARTESIAN_POINT('',(-0.127,-3.2,3.048));
#1785=VERTEX_POINT('',#1786);
#1784=EDGE_CURVE('',#1785,#1775,#1789,.T.);
#1789=LINE('',#1786,#1791);
#1791=VECTOR('',#1792,3.048);
#1792=DIRECTION('',(0.0,0.0,-1.0));
#1783=ORIENTED_EDGE('',*,*,#1784,.F.);
#1796=CARTESIAN_POINT('',(-0.127,-1.88,3.048));
#1795=VERTEX_POINT('',#1796);
#1794=EDGE_CURVE('',#1795,#1785,#1799,.T.);
#1799=LINE('',#1796,#1801);
#1801=VECTOR('',#1802,1.3208);
#1802=DIRECTION('',(0.0,-1.0,0.0));
#1793=ORIENTED_EDGE('',*,*,#1794,.F.);
#1806=CARTESIAN_POINT('',(-0.127,-1.88,0.0));
#1805=VERTEX_POINT('',#1806);
#1804=EDGE_CURVE('',#1805,#1795,#1809,.T.);
#1809=LINE('',#1806,#1811);
#1811=VECTOR('',#1812,3.048);
#1812=DIRECTION('',(0.0,0.0,1.0));
#1803=ORIENTED_EDGE('',*,*,#1804,.F.);
#1814=EDGE_CURVE('',#1716,#1805,#1819,.T.);
#1819=LINE('',#1717,#1821);
#1821=VECTOR('',#1822,0.381);
#1822=DIRECTION('',(0.0,1.0,0.0));
#1813=ORIENTED_EDGE('',*,*,#1814,.F.);
#1823=ORIENTED_EDGE('',*,*,#1715,.T.);
#1833=STYLED_ITEM('',(#34),#1834);
#1834=ADVANCED_FACE('',(#1840),#1835,.T.);
#1835=PLANE('',#1836);
#1836=AXIS2_PLACEMENT_3D('',#1837,#1838,#1839);
#1837=CARTESIAN_POINT('',(-0.127,-2.819,-2.54));
#1838=DIRECTION('',(0.0,-1.0,0.0));
#1839=DIRECTION('',(0.,0.,1.));
#1840=FACE_OUTER_BOUND('',#1841,.T.);
#1841=EDGE_LOOP('',(#1842,#1852,#1862,#1872));
#1842=ORIENTED_EDGE('',*,*,#1666,.T.);
#1855=CARTESIAN_POINT('',(0.127,-2.819,0.0));
#1854=VERTEX_POINT('',#1855);
#1853=EDGE_CURVE('',#1854,#1657,#1858,.T.);
#1858=LINE('',#1855,#1860);
#1860=VECTOR('',#1861,2.54);
#1861=DIRECTION('',(0.0,0.0,-1.0));
#1852=ORIENTED_EDGE('',*,*,#1853,.F.);
#1863=EDGE_CURVE('',#1765,#1854,#1868,.T.);
#1868=LINE('',#1766,#1870);
#1870=VECTOR('',#1871,0.254);
#1871=DIRECTION('',(1.0,0.0,0.0));
#1862=ORIENTED_EDGE('',*,*,#1863,.F.);
#1872=ORIENTED_EDGE('',*,*,#1764,.T.);
#1882=STYLED_ITEM('',(#34),#1883);
#1883=ADVANCED_FACE('',(#1889),#1884,.T.);
#1884=PLANE('',#1885);
#1885=AXIS2_PLACEMENT_3D('',#1886,#1887,#1888);
#1886=CARTESIAN_POINT('',(0.127,-2.819,-2.54));
#1887=DIRECTION('',(1.0,0.0,0.0));
#1888=DIRECTION('',(0.,0.,1.));
#1889=FACE_OUTER_BOUND('',#1890,.T.);
#1890=EDGE_LOOP('',(#1891,#1901,#1911,#1921,#1931,#1941,#1951,#1961));
#1891=ORIENTED_EDGE('',*,*,#1656,.T.);
#1901=ORIENTED_EDGE('',*,*,#1735,.T.);
#1914=CARTESIAN_POINT('',(0.127,-1.88,0.0));
#1913=VERTEX_POINT('',#1914);
#1912=EDGE_CURVE('',#1913,#1726,#1917,.T.);
#1917=LINE('',#1914,#1919);
#1919=VECTOR('',#1920,0.381);
#1920=DIRECTION('',(0.0,-1.0,0.0));
#1911=ORIENTED_EDGE('',*,*,#1912,.F.);
#1924=CARTESIAN_POINT('',(0.127,-1.88,2.794));
#1923=VERTEX_POINT('',#1924);
#1922=EDGE_CURVE('',#1923,#1913,#1927,.T.);
#1927=LINE('',#1924,#1929);
#1929=VECTOR('',#1930,2.794);
#1930=DIRECTION('',(0.0,0.0,-1.0));
#1921=ORIENTED_EDGE('',*,*,#1922,.F.);
#1934=CARTESIAN_POINT('',(0.127,-3.2,2.794));
#1933=VERTEX_POINT('',#1934);
#1932=EDGE_CURVE('',#1933,#1923,#1937,.T.);
#1937=LINE('',#1934,#1939);
#1939=VECTOR('',#1940,1.3208);
#1940=DIRECTION('',(0.0,1.0,0.0));
#1931=ORIENTED_EDGE('',*,*,#1932,.F.);
#1944=CARTESIAN_POINT('',(0.127,-3.2,0.0));
#1943=VERTEX_POINT('',#1944);
#1942=EDGE_CURVE('',#1943,#1933,#1947,.T.);
#1947=LINE('',#1944,#1949);
#1949=VECTOR('',#1950,2.794);
#1950=DIRECTION('',(0.0,0.0,1.0));
#1941=ORIENTED_EDGE('',*,*,#1942,.F.);
#1952=EDGE_CURVE('',#1854,#1943,#1957,.T.);
#1957=LINE('',#1855,#1959);
#1959=VECTOR('',#1960,0.381);
#1960=DIRECTION('',(0.0,-1.0,0.0));
#1951=ORIENTED_EDGE('',*,*,#1952,.F.);
#1961=ORIENTED_EDGE('',*,*,#1853,.T.);
#1971=STYLED_ITEM('',(#34),#1972);
#1972=ADVANCED_FACE('',(#1978),#1973,.T.);
#1973=PLANE('',#1974);
#1974=AXIS2_PLACEMENT_3D('',#1975,#1976,#1977);
#1975=CARTESIAN_POINT('',(0.127,-2.261,0.0));
#1976=DIRECTION('',(0.0,0.0,-1.0));
#1977=DIRECTION('',(0.,1.,0.));
#1978=FACE_OUTER_BOUND('',#1979,.T.);
#1979=EDGE_LOOP('',(#1980,#1990,#2000,#2010));
#1980=ORIENTED_EDGE('',*,*,#1725,.T.);
#1990=ORIENTED_EDGE('',*,*,#1814,.T.);
#2001=EDGE_CURVE('',#1913,#1805,#2006,.T.);
#2006=LINE('',#1914,#2008);
#2008=VECTOR('',#2009,0.254);
#2009=DIRECTION('',(-1.0,0.0,0.0));
#2000=ORIENTED_EDGE('',*,*,#2001,.F.);
#2010=ORIENTED_EDGE('',*,*,#1912,.T.);
#2020=STYLED_ITEM('',(#34),#2021);
#2021=ADVANCED_FACE('',(#2027),#2022,.T.);
#2022=PLANE('',#2023);
#2023=AXIS2_PLACEMENT_3D('',#2024,#2025,#2026);
#2024=CARTESIAN_POINT('',(0.127,-3.2,0.0));
#2025=DIRECTION('',(0.0,0.0,-1.0));
#2026=DIRECTION('',(0.,1.,0.));
#2027=FACE_OUTER_BOUND('',#2028,.T.);
#2028=EDGE_LOOP('',(#2029,#2039,#2049,#2059));
#2030=EDGE_CURVE('',#1775,#1943,#2035,.T.);
#2035=LINE('',#1776,#2037);
#2037=VECTOR('',#2038,0.254);
#2038=DIRECTION('',(1.0,0.0,0.0));
#2029=ORIENTED_EDGE('',*,*,#2030,.F.);
#2039=ORIENTED_EDGE('',*,*,#1774,.T.);
#2049=ORIENTED_EDGE('',*,*,#1863,.T.);
#2059=ORIENTED_EDGE('',*,*,#1952,.T.);
#2069=STYLED_ITEM('',(#34),#2070);
#2070=ADVANCED_FACE('',(#2076),#2071,.T.);
#2071=PLANE('',#2072);
#2072=AXIS2_PLACEMENT_3D('',#2073,#2074,#2075);
#2073=CARTESIAN_POINT('',(0.127,-1.88,0.0));
#2074=DIRECTION('',(0.0,1.0,0.0));
#2075=DIRECTION('',(0.,0.,1.));
#2076=FACE_OUTER_BOUND('',#2077,.T.);
#2077=EDGE_LOOP('',(#2078,#2088,#2098,#2108,#2118,#2128));
#2078=ORIENTED_EDGE('',*,*,#2001,.T.);
#2088=ORIENTED_EDGE('',*,*,#1804,.T.);
#2101=CARTESIAN_POINT('',(0.635,-1.88,3.048));
#2100=VERTEX_POINT('',#2101);
#2099=EDGE_CURVE('',#2100,#1795,#2104,.T.);
#2104=LINE('',#2101,#2106);
#2106=VECTOR('',#2107,0.762);
#2107=DIRECTION('',(-1.0,0.0,0.0));
#2098=ORIENTED_EDGE('',*,*,#2099,.F.);
#2111=CARTESIAN_POINT('',(0.635,-1.88,2.794));
#2110=VERTEX_POINT('',#2111);
#2109=EDGE_CURVE('',#2110,#2100,#2114,.T.);
#2114=LINE('',#2111,#2116);
#2116=VECTOR('',#2117,0.254);
#2117=DIRECTION('',(0.0,0.0,1.0));
#2108=ORIENTED_EDGE('',*,*,#2109,.F.);
#2119=EDGE_CURVE('',#1923,#2110,#2124,.T.);
#2124=LINE('',#1924,#2126);
#2126=VECTOR('',#2127,0.508);
#2127=DIRECTION('',(1.0,0.0,0.0));
#2118=ORIENTED_EDGE('',*,*,#2119,.F.);
#2128=ORIENTED_EDGE('',*,*,#1922,.T.);
#2138=STYLED_ITEM('',(#34),#2139);
#2139=ADVANCED_FACE('',(#2145),#2140,.T.);
#2140=PLANE('',#2141);
#2141=AXIS2_PLACEMENT_3D('',#2142,#2143,#2144);
#2142=CARTESIAN_POINT('',(-0.127,-3.2,0.0));
#2143=DIRECTION('',(0.0,-1.0,0.0));
#2144=DIRECTION('',(0.,0.,1.));
#2145=FACE_OUTER_BOUND('',#2146,.T.);
#2146=EDGE_LOOP('',(#2147,#2157,#2167,#2177,#2187,#2197));
#2147=ORIENTED_EDGE('',*,*,#2030,.T.);
#2157=ORIENTED_EDGE('',*,*,#1942,.T.);
#2170=CARTESIAN_POINT('',(0.635,-3.2,2.794));
#2169=VERTEX_POINT('',#2170);
#2168=EDGE_CURVE('',#2169,#1933,#2173,.T.);
#2173=LINE('',#2170,#2175);
#2175=VECTOR('',#2176,0.508);
#2176=DIRECTION('',(-1.0,0.0,0.0));
#2167=ORIENTED_EDGE('',*,*,#2168,.F.);
#2180=CARTESIAN_POINT('',(0.635,-3.2,3.048));
#2179=VERTEX_POINT('',#2180);
#2178=EDGE_CURVE('',#2179,#2169,#2183,.T.);
#2183=LINE('',#2180,#2185);
#2185=VECTOR('',#2186,0.254);
#2186=DIRECTION('',(0.0,0.0,-1.0));
#2177=ORIENTED_EDGE('',*,*,#2178,.F.);
#2188=EDGE_CURVE('',#1785,#2179,#2193,.T.);
#2193=LINE('',#1786,#2195);
#2195=VECTOR('',#2196,0.762);
#2196=DIRECTION('',(1.0,0.0,0.0));
#2187=ORIENTED_EDGE('',*,*,#2188,.F.);
#2197=ORIENTED_EDGE('',*,*,#1784,.T.);
#2207=STYLED_ITEM('',(#34),#2208);
#2208=ADVANCED_FACE('',(#2214),#2209,.T.);
#2209=PLANE('',#2210);
#2210=AXIS2_PLACEMENT_3D('',#2211,#2212,#2213);
#2211=CARTESIAN_POINT('',(0.635,-3.2,2.794));
#2212=DIRECTION('',(0.0,0.0,-1.0));
#2213=DIRECTION('',(0.,1.,0.));
#2214=FACE_OUTER_BOUND('',#2215,.T.);
#2215=EDGE_LOOP('',(#2216,#2226,#2236,#2246));
#2216=ORIENTED_EDGE('',*,*,#2168,.T.);
#2226=ORIENTED_EDGE('',*,*,#1932,.T.);
#2236=ORIENTED_EDGE('',*,*,#2119,.T.);
#2247=EDGE_CURVE('',#2169,#2110,#2252,.T.);
#2252=LINE('',#2170,#2254);
#2254=VECTOR('',#2255,1.3208);
#2255=DIRECTION('',(0.0,1.0,0.0));
#2246=ORIENTED_EDGE('',*,*,#2247,.F.);
#2256=STYLED_ITEM('',(#34),#2257);
#2257=ADVANCED_FACE('',(#2263),#2258,.T.);
#2258=PLANE('',#2259);
#2259=AXIS2_PLACEMENT_3D('',#2260,#2261,#2262);
#2260=CARTESIAN_POINT('',(0.635,-3.2,2.794));
#2261=DIRECTION('',(1.0,0.0,0.0));
#2262=DIRECTION('',(0.,0.,1.));
#2263=FACE_OUTER_BOUND('',#2264,.T.);
#2264=EDGE_LOOP('',(#2265,#2275,#2285,#2295));
#2265=ORIENTED_EDGE('',*,*,#2247,.T.);
#2275=ORIENTED_EDGE('',*,*,#2109,.T.);
#2286=EDGE_CURVE('',#2179,#2100,#2291,.T.);
#2291=LINE('',#2180,#2293);
#2293=VECTOR('',#2294,1.3208);
#2294=DIRECTION('',(0.0,1.0,0.0));
#2285=ORIENTED_EDGE('',*,*,#2286,.F.);
#2295=ORIENTED_EDGE('',*,*,#2178,.T.);
#2305=STYLED_ITEM('',(#34),#2306);
#2306=ADVANCED_FACE('',(#2312),#2307,.T.);
#2307=PLANE('',#2308);
#2308=AXIS2_PLACEMENT_3D('',#2309,#2310,#2311);
#2309=CARTESIAN_POINT('',(0.635,-1.88,3.048));
#2310=DIRECTION('',(0.0,0.0,1.0));
#2311=DIRECTION('',(0.,1.,0.));
#2312=FACE_OUTER_BOUND('',#2313,.T.);
#2313=EDGE_LOOP('',(#2314,#2324,#2334,#2344));
#2314=ORIENTED_EDGE('',*,*,#2099,.T.);
#2324=ORIENTED_EDGE('',*,*,#1794,.T.);
#2334=ORIENTED_EDGE('',*,*,#2188,.T.);
#2344=ORIENTED_EDGE('',*,*,#2286,.T.);
#2355=MANIFOLD_SOLID_BREP($,#2356);
#2356=CLOSED_SHELL('',(#2358,#2407,#2456,#2545,#2594,#2683,#2732,#2781,#2850,#2919,#2968,#3017));
#2357=STYLED_ITEM('',(#34),#2358);
#2358=ADVANCED_FACE('',(#2364),#2359,.T.);
#2359=PLANE('',#2360);
#2360=AXIS2_PLACEMENT_3D('',#2361,#2362,#2363);
#2361=CARTESIAN_POINT('',(0.127,-4.801,-2.54));
#2362=DIRECTION('',(0.0,0.0,-1.0));
#2363=DIRECTION('',(0.,1.,0.));
#2364=FACE_OUTER_BOUND('',#2365,.T.);
#2365=EDGE_LOOP('',(#2366,#2376,#2386,#2396));
#2369=CARTESIAN_POINT('',(0.127,-5.359,-2.54));
#2368=VERTEX_POINT('',#2369);
#2371=CARTESIAN_POINT('',(0.127,-4.801,-2.54));
#2370=VERTEX_POINT('',#2371);
#2367=EDGE_CURVE('',#2368,#2370,#2372,.T.);
#2372=LINE('',#2369,#2374);
#2374=VECTOR('',#2375,0.5588);
#2375=DIRECTION('',(0.0,1.0,0.0));
#2366=ORIENTED_EDGE('',*,*,#2367,.F.);
#2379=CARTESIAN_POINT('',(-0.127,-5.359,-2.54));
#2378=VERTEX_POINT('',#2379);
#2377=EDGE_CURVE('',#2378,#2368,#2382,.T.);
#2382=LINE('',#2379,#2384);
#2384=VECTOR('',#2385,0.254);
#2385=DIRECTION('',(1.0,0.0,0.0));
#2376=ORIENTED_EDGE('',*,*,#2377,.F.);
#2389=CARTESIAN_POINT('',(-0.127,-4.801,-2.54));
#2388=VERTEX_POINT('',#2389);
#2387=EDGE_CURVE('',#2388,#2378,#2392,.T.);
#2392=LINE('',#2389,#2394);
#2394=VECTOR('',#2395,0.5588);
#2395=DIRECTION('',(0.0,-1.0,0.0));
#2386=ORIENTED_EDGE('',*,*,#2387,.F.);
#2397=EDGE_CURVE('',#2370,#2388,#2402,.T.);
#2402=LINE('',#2371,#2404);
#2404=VECTOR('',#2405,0.254);
#2405=DIRECTION('',(-1.0,0.0,0.0));
#2396=ORIENTED_EDGE('',*,*,#2397,.F.);
#2406=STYLED_ITEM('',(#34),#2407);
#2407=ADVANCED_FACE('',(#2413),#2408,.T.);
#2408=PLANE('',#2409);
#2409=AXIS2_PLACEMENT_3D('',#2410,#2411,#2412);
#2410=CARTESIAN_POINT('',(0.127,-4.801,-2.54));
#2411=DIRECTION('',(0.0,1.0,0.0));
#2412=DIRECTION('',(0.,0.,1.));
#2413=FACE_OUTER_BOUND('',#2414,.T.);
#2414=EDGE_LOOP('',(#2415,#2425,#2435,#2445));
#2415=ORIENTED_EDGE('',*,*,#2397,.T.);
#2428=CARTESIAN_POINT('',(-0.127,-4.801,0.0));
#2427=VERTEX_POINT('',#2428);
#2426=EDGE_CURVE('',#2427,#2388,#2431,.T.);
#2431=LINE('',#2428,#2433);
#2433=VECTOR('',#2434,2.54);
#2434=DIRECTION('',(0.0,0.0,-1.0));
#2425=ORIENTED_EDGE('',*,*,#2426,.F.);
#2438=CARTESIAN_POINT('',(0.127,-4.801,0.0));
#2437=VERTEX_POINT('',#2438);
#2436=EDGE_CURVE('',#2437,#2427,#2441,.T.);
#2441=LINE('',#2438,#2443);
#2443=VECTOR('',#2444,0.254);
#2444=DIRECTION('',(-1.0,0.0,0.0));
#2435=ORIENTED_EDGE('',*,*,#2436,.F.);
#2446=EDGE_CURVE('',#2370,#2437,#2451,.T.);
#2451=LINE('',#2371,#2453);
#2453=VECTOR('',#2454,2.54);
#2454=DIRECTION('',(0.0,0.0,1.0));
#2445=ORIENTED_EDGE('',*,*,#2446,.F.);
#2455=STYLED_ITEM('',(#34),#2456);
#2456=ADVANCED_FACE('',(#2462),#2457,.T.);
#2457=PLANE('',#2458);
#2458=AXIS2_PLACEMENT_3D('',#2459,#2460,#2461);
#2459=CARTESIAN_POINT('',(-0.127,-4.801,-2.54));
#2460=DIRECTION('',(-1.0,0.0,0.0));
#2461=DIRECTION('',(0.,0.,1.));
#2462=FACE_OUTER_BOUND('',#2463,.T.);
#2463=EDGE_LOOP('',(#2464,#2474,#2484,#2494,#2504,#2514,#2524,#2534));
#2464=ORIENTED_EDGE('',*,*,#2387,.T.);
#2477=CARTESIAN_POINT('',(-0.127,-5.359,0.0));
#2476=VERTEX_POINT('',#2477);
#2475=EDGE_CURVE('',#2476,#2378,#2480,.T.);
#2480=LINE('',#2477,#2482);
#2482=VECTOR('',#2483,2.54);
#2483=DIRECTION('',(0.0,0.0,-1.0));
#2474=ORIENTED_EDGE('',*,*,#2475,.F.);
#2487=CARTESIAN_POINT('',(-0.127,-5.74,0.0));
#2486=VERTEX_POINT('',#2487);
#2485=EDGE_CURVE('',#2486,#2476,#2490,.T.);
#2490=LINE('',#2487,#2492);
#2492=VECTOR('',#2493,0.381);
#2493=DIRECTION('',(0.0,1.0,0.0));
#2484=ORIENTED_EDGE('',*,*,#2485,.F.);
#2497=CARTESIAN_POINT('',(-0.127,-5.74,3.048));
#2496=VERTEX_POINT('',#2497);
#2495=EDGE_CURVE('',#2496,#2486,#2500,.T.);
#2500=LINE('',#2497,#2502);
#2502=VECTOR('',#2503,3.048);
#2503=DIRECTION('',(0.0,0.0,-1.0));
#2494=ORIENTED_EDGE('',*,*,#2495,.F.);
#2507=CARTESIAN_POINT('',(-0.127,-4.42,3.048));
#2506=VERTEX_POINT('',#2507);
#2505=EDGE_CURVE('',#2506,#2496,#2510,.T.);
#2510=LINE('',#2507,#2512);
#2512=VECTOR('',#2513,1.3208);
#2513=DIRECTION('',(0.0,-1.0,0.0));
#2504=ORIENTED_EDGE('',*,*,#2505,.F.);
#2517=CARTESIAN_POINT('',(-0.127,-4.42,0.0));
#2516=VERTEX_POINT('',#2517);
#2515=EDGE_CURVE('',#2516,#2506,#2520,.T.);
#2520=LINE('',#2517,#2522);
#2522=VECTOR('',#2523,3.048);
#2523=DIRECTION('',(0.0,0.0,1.0));
#2514=ORIENTED_EDGE('',*,*,#2515,.F.);
#2525=EDGE_CURVE('',#2427,#2516,#2530,.T.);
#2530=LINE('',#2428,#2532);
#2532=VECTOR('',#2533,0.381);
#2533=DIRECTION('',(0.0,1.0,0.0));
#2524=ORIENTED_EDGE('',*,*,#2525,.F.);
#2534=ORIENTED_EDGE('',*,*,#2426,.T.);
#2544=STYLED_ITEM('',(#34),#2545);
#2545=ADVANCED_FACE('',(#2551),#2546,.T.);
#2546=PLANE('',#2547);
#2547=AXIS2_PLACEMENT_3D('',#2548,#2549,#2550);
#2548=CARTESIAN_POINT('',(-0.127,-5.359,-2.54));
#2549=DIRECTION('',(0.0,-1.0,0.0));
#2550=DIRECTION('',(0.,0.,1.));
#2551=FACE_OUTER_BOUND('',#2552,.T.);
#2552=EDGE_LOOP('',(#2553,#2563,#2573,#2583));
#2553=ORIENTED_EDGE('',*,*,#2377,.T.);
#2566=CARTESIAN_POINT('',(0.127,-5.359,0.0));
#2565=VERTEX_POINT('',#2566);
#2564=EDGE_CURVE('',#2565,#2368,#2569,.T.);
#2569=LINE('',#2566,#2571);
#2571=VECTOR('',#2572,2.54);
#2572=DIRECTION('',(0.0,0.0,-1.0));
#2563=ORIENTED_EDGE('',*,*,#2564,.F.);
#2574=EDGE_CURVE('',#2476,#2565,#2579,.T.);
#2579=LINE('',#2477,#2581);
#2581=VECTOR('',#2582,0.254);
#2582=DIRECTION('',(1.0,0.0,0.0));
#2573=ORIENTED_EDGE('',*,*,#2574,.F.);
#2583=ORIENTED_EDGE('',*,*,#2475,.T.);
#2593=STYLED_ITEM('',(#34),#2594);
#2594=ADVANCED_FACE('',(#2600),#2595,.T.);
#2595=PLANE('',#2596);
#2596=AXIS2_PLACEMENT_3D('',#2597,#2598,#2599);
#2597=CARTESIAN_POINT('',(0.127,-5.359,-2.54));
#2598=DIRECTION('',(1.0,0.0,0.0));
#2599=DIRECTION('',(0.,0.,1.));
#2600=FACE_OUTER_BOUND('',#2601,.T.);
#2601=EDGE_LOOP('',(#2602,#2612,#2622,#2632,#2642,#2652,#2662,#2672));
#2602=ORIENTED_EDGE('',*,*,#2367,.T.);
#2612=ORIENTED_EDGE('',*,*,#2446,.T.);
#2625=CARTESIAN_POINT('',(0.127,-4.42,0.0));
#2624=VERTEX_POINT('',#2625);
#2623=EDGE_CURVE('',#2624,#2437,#2628,.T.);
#2628=LINE('',#2625,#2630);
#2630=VECTOR('',#2631,0.381);
#2631=DIRECTION('',(0.0,-1.0,0.0));
#2622=ORIENTED_EDGE('',*,*,#2623,.F.);
#2635=CARTESIAN_POINT('',(0.127,-4.42,2.794));
#2634=VERTEX_POINT('',#2635);
#2633=EDGE_CURVE('',#2634,#2624,#2638,.T.);
#2638=LINE('',#2635,#2640);
#2640=VECTOR('',#2641,2.794);
#2641=DIRECTION('',(0.0,0.0,-1.0));
#2632=ORIENTED_EDGE('',*,*,#2633,.F.);
#2645=CARTESIAN_POINT('',(0.127,-5.74,2.794));
#2644=VERTEX_POINT('',#2645);
#2643=EDGE_CURVE('',#2644,#2634,#2648,.T.);
#2648=LINE('',#2645,#2650);
#2650=VECTOR('',#2651,1.3208);
#2651=DIRECTION('',(0.0,1.0,0.0));
#2642=ORIENTED_EDGE('',*,*,#2643,.F.);
#2655=CARTESIAN_POINT('',(0.127,-5.74,0.0));
#2654=VERTEX_POINT('',#2655);
#2653=EDGE_CURVE('',#2654,#2644,#2658,.T.);
#2658=LINE('',#2655,#2660);
#2660=VECTOR('',#2661,2.794);
#2661=DIRECTION('',(0.0,0.0,1.0));
#2652=ORIENTED_EDGE('',*,*,#2653,.F.);
#2663=EDGE_CURVE('',#2565,#2654,#2668,.T.);
#2668=LINE('',#2566,#2670);
#2670=VECTOR('',#2671,0.381);
#2671=DIRECTION('',(0.0,-1.0,0.0));
#2662=ORIENTED_EDGE('',*,*,#2663,.F.);
#2672=ORIENTED_EDGE('',*,*,#2564,.T.);
#2682=STYLED_ITEM('',(#34),#2683);
#2683=ADVANCED_FACE('',(#2689),#2684,.T.);
#2684=PLANE('',#2685);
#2685=AXIS2_PLACEMENT_3D('',#2686,#2687,#2688);
#2686=CARTESIAN_POINT('',(0.127,-4.801,0.0));
#2687=DIRECTION('',(0.0,0.0,-1.0));
#2688=DIRECTION('',(0.,1.,0.));
#2689=FACE_OUTER_BOUND('',#2690,.T.);
#2690=EDGE_LOOP('',(#2691,#2701,#2711,#2721));
#2691=ORIENTED_EDGE('',*,*,#2436,.T.);
#2701=ORIENTED_EDGE('',*,*,#2525,.T.);
#2712=EDGE_CURVE('',#2624,#2516,#2717,.T.);
#2717=LINE('',#2625,#2719);
#2719=VECTOR('',#2720,0.254);
#2720=DIRECTION('',(-1.0,0.0,0.0));
#2711=ORIENTED_EDGE('',*,*,#2712,.F.);
#2721=ORIENTED_EDGE('',*,*,#2623,.T.);
#2731=STYLED_ITEM('',(#34),#2732);
#2732=ADVANCED_FACE('',(#2738),#2733,.T.);
#2733=PLANE('',#2734);
#2734=AXIS2_PLACEMENT_3D('',#2735,#2736,#2737);
#2735=CARTESIAN_POINT('',(0.127,-5.74,0.0));
#2736=DIRECTION('',(0.0,0.0,-1.0));
#2737=DIRECTION('',(0.,1.,0.));
#2738=FACE_OUTER_BOUND('',#2739,.T.);
#2739=EDGE_LOOP('',(#2740,#2750,#2760,#2770));
#2741=EDGE_CURVE('',#2486,#2654,#2746,.T.);
#2746=LINE('',#2487,#2748);
#2748=VECTOR('',#2749,0.254);
#2749=DIRECTION('',(1.0,0.0,0.0));
#2740=ORIENTED_EDGE('',*,*,#2741,.F.);
#2750=ORIENTED_EDGE('',*,*,#2485,.T.);
#2760=ORIENTED_EDGE('',*,*,#2574,.T.);
#2770=ORIENTED_EDGE('',*,*,#2663,.T.);
#2780=STYLED_ITEM('',(#34),#2781);
#2781=ADVANCED_FACE('',(#2787),#2782,.T.);
#2782=PLANE('',#2783);
#2783=AXIS2_PLACEMENT_3D('',#2784,#2785,#2786);
#2784=CARTESIAN_POINT('',(0.127,-4.42,0.0));
#2785=DIRECTION('',(0.0,1.0,0.0));
#2786=DIRECTION('',(0.,0.,1.));
#2787=FACE_OUTER_BOUND('',#2788,.T.);
#2788=EDGE_LOOP('',(#2789,#2799,#2809,#2819,#2829,#2839));
#2789=ORIENTED_EDGE('',*,*,#2712,.T.);
#2799=ORIENTED_EDGE('',*,*,#2515,.T.);
#2812=CARTESIAN_POINT('',(0.635,-4.42,3.048));
#2811=VERTEX_POINT('',#2812);
#2810=EDGE_CURVE('',#2811,#2506,#2815,.T.);
#2815=LINE('',#2812,#2817);
#2817=VECTOR('',#2818,0.762);
#2818=DIRECTION('',(-1.0,0.0,0.0));
#2809=ORIENTED_EDGE('',*,*,#2810,.F.);
#2822=CARTESIAN_POINT('',(0.635,-4.42,2.794));
#2821=VERTEX_POINT('',#2822);
#2820=EDGE_CURVE('',#2821,#2811,#2825,.T.);
#2825=LINE('',#2822,#2827);
#2827=VECTOR('',#2828,0.254);
#2828=DIRECTION('',(0.0,0.0,1.0));
#2819=ORIENTED_EDGE('',*,*,#2820,.F.);
#2830=EDGE_CURVE('',#2634,#2821,#2835,.T.);
#2835=LINE('',#2635,#2837);
#2837=VECTOR('',#2838,0.508);
#2838=DIRECTION('',(1.0,0.0,0.0));
#2829=ORIENTED_EDGE('',*,*,#2830,.F.);
#2839=ORIENTED_EDGE('',*,*,#2633,.T.);
#2849=STYLED_ITEM('',(#34),#2850);
#2850=ADVANCED_FACE('',(#2856),#2851,.T.);
#2851=PLANE('',#2852);
#2852=AXIS2_PLACEMENT_3D('',#2853,#2854,#2855);
#2853=CARTESIAN_POINT('',(-0.127,-5.74,0.0));
#2854=DIRECTION('',(0.0,-1.0,0.0));
#2855=DIRECTION('',(0.,0.,1.));
#2856=FACE_OUTER_BOUND('',#2857,.T.);
#2857=EDGE_LOOP('',(#2858,#2868,#2878,#2888,#2898,#2908));
#2858=ORIENTED_EDGE('',*,*,#2741,.T.);
#2868=ORIENTED_EDGE('',*,*,#2653,.T.);
#2881=CARTESIAN_POINT('',(0.635,-5.74,2.794));
#2880=VERTEX_POINT('',#2881);
#2879=EDGE_CURVE('',#2880,#2644,#2884,.T.);
#2884=LINE('',#2881,#2886);
#2886=VECTOR('',#2887,0.508);
#2887=DIRECTION('',(-1.0,0.0,0.0));
#2878=ORIENTED_EDGE('',*,*,#2879,.F.);
#2891=CARTESIAN_POINT('',(0.635,-5.74,3.048));
#2890=VERTEX_POINT('',#2891);
#2889=EDGE_CURVE('',#2890,#2880,#2894,.T.);
#2894=LINE('',#2891,#2896);
#2896=VECTOR('',#2897,0.254);
#2897=DIRECTION('',(0.0,0.0,-1.0));
#2888=ORIENTED_EDGE('',*,*,#2889,.F.);
#2899=EDGE_CURVE('',#2496,#2890,#2904,.T.);
#2904=LINE('',#2497,#2906);
#2906=VECTOR('',#2907,0.762);
#2907=DIRECTION('',(1.0,0.0,0.0));
#2898=ORIENTED_EDGE('',*,*,#2899,.F.);
#2908=ORIENTED_EDGE('',*,*,#2495,.T.);
#2918=STYLED_ITEM('',(#34),#2919);
#2919=ADVANCED_FACE('',(#2925),#2920,.T.);
#2920=PLANE('',#2921);
#2921=AXIS2_PLACEMENT_3D('',#2922,#2923,#2924);
#2922=CARTESIAN_POINT('',(0.635,-5.74,2.794));
#2923=DIRECTION('',(0.0,0.0,-1.0));
#2924=DIRECTION('',(0.,1.,0.));
#2925=FACE_OUTER_BOUND('',#2926,.T.);
#2926=EDGE_LOOP('',(#2927,#2937,#2947,#2957));
#2927=ORIENTED_EDGE('',*,*,#2879,.T.);
#2937=ORIENTED_EDGE('',*,*,#2643,.T.);
#2947=ORIENTED_EDGE('',*,*,#2830,.T.);
#2958=EDGE_CURVE('',#2880,#2821,#2963,.T.);
#2963=LINE('',#2881,#2965);
#2965=VECTOR('',#2966,1.3208);
#2966=DIRECTION('',(0.0,1.0,0.0));
#2957=ORIENTED_EDGE('',*,*,#2958,.F.);
#2967=STYLED_ITEM('',(#34),#2968);
#2968=ADVANCED_FACE('',(#2974),#2969,.T.);
#2969=PLANE('',#2970);
#2970=AXIS2_PLACEMENT_3D('',#2971,#2972,#2973);
#2971=CARTESIAN_POINT('',(0.635,-5.74,2.794));
#2972=DIRECTION('',(1.0,0.0,0.0));
#2973=DIRECTION('',(0.,0.,1.));
#2974=FACE_OUTER_BOUND('',#2975,.T.);
#2975=EDGE_LOOP('',(#2976,#2986,#2996,#3006));
#2976=ORIENTED_EDGE('',*,*,#2958,.T.);
#2986=ORIENTED_EDGE('',*,*,#2820,.T.);
#2997=EDGE_CURVE('',#2890,#2811,#3002,.T.);
#3002=LINE('',#2891,#3004);
#3004=VECTOR('',#3005,1.3208);
#3005=DIRECTION('',(0.0,1.0,0.0));
#2996=ORIENTED_EDGE('',*,*,#2997,.F.);
#3006=ORIENTED_EDGE('',*,*,#2889,.T.);
#3016=STYLED_ITEM('',(#34),#3017);
#3017=ADVANCED_FACE('',(#3023),#3018,.T.);
#3018=PLANE('',#3019);
#3019=AXIS2_PLACEMENT_3D('',#3020,#3021,#3022);
#3020=CARTESIAN_POINT('',(0.635,-4.42,3.048));
#3021=DIRECTION('',(0.0,0.0,1.0));
#3022=DIRECTION('',(0.,1.,0.));
#3023=FACE_OUTER_BOUND('',#3024,.T.);
#3024=EDGE_LOOP('',(#3025,#3035,#3045,#3055));
#3025=ORIENTED_EDGE('',*,*,#2810,.T.);
#3035=ORIENTED_EDGE('',*,*,#2505,.T.);
#3045=ORIENTED_EDGE('',*,*,#2899,.T.);
#3055=ORIENTED_EDGE('',*,*,#2997,.T.);
#3066=MANIFOLD_SOLID_BREP($,#3067);
#3067=CLOSED_SHELL('',(#3069,#3118,#3167,#3256,#3305,#3394,#3443,#3492,#3561,#3630,#3679,#3728));
#3068=STYLED_ITEM('',(#34),#3069);
#3069=ADVANCED_FACE('',(#3075),#3070,.T.);
#3070=PLANE('',#3071);
#3071=AXIS2_PLACEMENT_3D('',#3072,#3073,#3074);
#3072=CARTESIAN_POINT('',(0.127,-7.341,-2.54));
#3073=DIRECTION('',(0.0,0.0,-1.0));
#3074=DIRECTION('',(0.,1.,0.));
#3075=FACE_OUTER_BOUND('',#3076,.T.);
#3076=EDGE_LOOP('',(#3077,#3087,#3097,#3107));
#3080=CARTESIAN_POINT('',(0.127,-7.899,-2.54));
#3079=VERTEX_POINT('',#3080);
#3082=CARTESIAN_POINT('',(0.127,-7.341,-2.54));
#3081=VERTEX_POINT('',#3082);
#3078=EDGE_CURVE('',#3079,#3081,#3083,.T.);
#3083=LINE('',#3080,#3085);
#3085=VECTOR('',#3086,0.5588);
#3086=DIRECTION('',(0.0,1.0,0.0));
#3077=ORIENTED_EDGE('',*,*,#3078,.F.);
#3090=CARTESIAN_POINT('',(-0.127,-7.899,-2.54));
#3089=VERTEX_POINT('',#3090);
#3088=EDGE_CURVE('',#3089,#3079,#3093,.T.);
#3093=LINE('',#3090,#3095);
#3095=VECTOR('',#3096,0.254);
#3096=DIRECTION('',(1.0,0.0,0.0));
#3087=ORIENTED_EDGE('',*,*,#3088,.F.);
#3100=CARTESIAN_POINT('',(-0.127,-7.341,-2.54));
#3099=VERTEX_POINT('',#3100);
#3098=EDGE_CURVE('',#3099,#3089,#3103,.T.);
#3103=LINE('',#3100,#3105);
#3105=VECTOR('',#3106,0.5588);
#3106=DIRECTION('',(0.0,-1.0,0.0));
#3097=ORIENTED_EDGE('',*,*,#3098,.F.);
#3108=EDGE_CURVE('',#3081,#3099,#3113,.T.);
#3113=LINE('',#3082,#3115);
#3115=VECTOR('',#3116,0.254);
#3116=DIRECTION('',(-1.0,0.0,0.0));
#3107=ORIENTED_EDGE('',*,*,#3108,.F.);
#3117=STYLED_ITEM('',(#34),#3118);
#3118=ADVANCED_FACE('',(#3124),#3119,.T.);
#3119=PLANE('',#3120);
#3120=AXIS2_PLACEMENT_3D('',#3121,#3122,#3123);
#3121=CARTESIAN_POINT('',(0.127,-7.341,-2.54));
#3122=DIRECTION('',(0.0,1.0,0.0));
#3123=DIRECTION('',(0.,0.,1.));
#3124=FACE_OUTER_BOUND('',#3125,.T.);
#3125=EDGE_LOOP('',(#3126,#3136,#3146,#3156));
#3126=ORIENTED_EDGE('',*,*,#3108,.T.);
#3139=CARTESIAN_POINT('',(-0.127,-7.341,0.0));
#3138=VERTEX_POINT('',#3139);
#3137=EDGE_CURVE('',#3138,#3099,#3142,.T.);
#3142=LINE('',#3139,#3144);
#3144=VECTOR('',#3145,2.54);
#3145=DIRECTION('',(0.0,0.0,-1.0));
#3136=ORIENTED_EDGE('',*,*,#3137,.F.);
#3149=CARTESIAN_POINT('',(0.127,-7.341,0.0));
#3148=VERTEX_POINT('',#3149);
#3147=EDGE_CURVE('',#3148,#3138,#3152,.T.);
#3152=LINE('',#3149,#3154);
#3154=VECTOR('',#3155,0.254);
#3155=DIRECTION('',(-1.0,0.0,0.0));
#3146=ORIENTED_EDGE('',*,*,#3147,.F.);
#3157=EDGE_CURVE('',#3081,#3148,#3162,.T.);
#3162=LINE('',#3082,#3164);
#3164=VECTOR('',#3165,2.54);
#3165=DIRECTION('',(0.0,0.0,1.0));
#3156=ORIENTED_EDGE('',*,*,#3157,.F.);
#3166=STYLED_ITEM('',(#34),#3167);
#3167=ADVANCED_FACE('',(#3173),#3168,.T.);
#3168=PLANE('',#3169);
#3169=AXIS2_PLACEMENT_3D('',#3170,#3171,#3172);
#3170=CARTESIAN_POINT('',(-0.127,-7.341,-2.54));
#3171=DIRECTION('',(-1.0,0.0,0.0));
#3172=DIRECTION('',(0.,0.,1.));
#3173=FACE_OUTER_BOUND('',#3174,.T.);
#3174=EDGE_LOOP('',(#3175,#3185,#3195,#3205,#3215,#3225,#3235,#3245));
#3175=ORIENTED_EDGE('',*,*,#3098,.T.);
#3188=CARTESIAN_POINT('',(-0.127,-7.899,0.0));
#3187=VERTEX_POINT('',#3188);
#3186=EDGE_CURVE('',#3187,#3089,#3191,.T.);
#3191=LINE('',#3188,#3193);
#3193=VECTOR('',#3194,2.54);
#3194=DIRECTION('',(0.0,0.0,-1.0));
#3185=ORIENTED_EDGE('',*,*,#3186,.F.);
#3198=CARTESIAN_POINT('',(-0.127,-8.28,0.0));
#3197=VERTEX_POINT('',#3198);
#3196=EDGE_CURVE('',#3197,#3187,#3201,.T.);
#3201=LINE('',#3198,#3203);
#3203=VECTOR('',#3204,0.381);
#3204=DIRECTION('',(0.0,1.0,0.0));
#3195=ORIENTED_EDGE('',*,*,#3196,.F.);
#3208=CARTESIAN_POINT('',(-0.127,-8.28,3.048));
#3207=VERTEX_POINT('',#3208);
#3206=EDGE_CURVE('',#3207,#3197,#3211,.T.);
#3211=LINE('',#3208,#3213);
#3213=VECTOR('',#3214,3.048);
#3214=DIRECTION('',(0.0,0.0,-1.0));
#3205=ORIENTED_EDGE('',*,*,#3206,.F.);
#3218=CARTESIAN_POINT('',(-0.127,-6.96,3.048));
#3217=VERTEX_POINT('',#3218);
#3216=EDGE_CURVE('',#3217,#3207,#3221,.T.);
#3221=LINE('',#3218,#3223);
#3223=VECTOR('',#3224,1.3208);
#3224=DIRECTION('',(0.0,-1.0,0.0));
#3215=ORIENTED_EDGE('',*,*,#3216,.F.);
#3228=CARTESIAN_POINT('',(-0.127,-6.96,0.0));
#3227=VERTEX_POINT('',#3228);
#3226=EDGE_CURVE('',#3227,#3217,#3231,.T.);
#3231=LINE('',#3228,#3233);
#3233=VECTOR('',#3234,3.048);
#3234=DIRECTION('',(0.0,0.0,1.0));
#3225=ORIENTED_EDGE('',*,*,#3226,.F.);
#3236=EDGE_CURVE('',#3138,#3227,#3241,.T.);
#3241=LINE('',#3139,#3243);
#3243=VECTOR('',#3244,0.381);
#3244=DIRECTION('',(0.0,1.0,0.0));
#3235=ORIENTED_EDGE('',*,*,#3236,.F.);
#3245=ORIENTED_EDGE('',*,*,#3137,.T.);
#3255=STYLED_ITEM('',(#34),#3256);
#3256=ADVANCED_FACE('',(#3262),#3257,.T.);
#3257=PLANE('',#3258);
#3258=AXIS2_PLACEMENT_3D('',#3259,#3260,#3261);
#3259=CARTESIAN_POINT('',(-0.127,-7.899,-2.54));
#3260=DIRECTION('',(0.0,-1.0,0.0));
#3261=DIRECTION('',(0.,0.,1.));
#3262=FACE_OUTER_BOUND('',#3263,.T.);
#3263=EDGE_LOOP('',(#3264,#3274,#3284,#3294));
#3264=ORIENTED_EDGE('',*,*,#3088,.T.);
#3277=CARTESIAN_POINT('',(0.127,-7.899,0.0));
#3276=VERTEX_POINT('',#3277);
#3275=EDGE_CURVE('',#3276,#3079,#3280,.T.);
#3280=LINE('',#3277,#3282);
#3282=VECTOR('',#3283,2.54);
#3283=DIRECTION('',(0.0,0.0,-1.0));
#3274=ORIENTED_EDGE('',*,*,#3275,.F.);
#3285=EDGE_CURVE('',#3187,#3276,#3290,.T.);
#3290=LINE('',#3188,#3292);
#3292=VECTOR('',#3293,0.254);
#3293=DIRECTION('',(1.0,0.0,0.0));
#3284=ORIENTED_EDGE('',*,*,#3285,.F.);
#3294=ORIENTED_EDGE('',*,*,#3186,.T.);
#3304=STYLED_ITEM('',(#34),#3305);
#3305=ADVANCED_FACE('',(#3311),#3306,.T.);
#3306=PLANE('',#3307);
#3307=AXIS2_PLACEMENT_3D('',#3308,#3309,#3310);
#3308=CARTESIAN_POINT('',(0.127,-7.899,-2.54));
#3309=DIRECTION('',(1.0,0.0,0.0));
#3310=DIRECTION('',(0.,0.,1.));
#3311=FACE_OUTER_BOUND('',#3312,.T.);
#3312=EDGE_LOOP('',(#3313,#3323,#3333,#3343,#3353,#3363,#3373,#3383));
#3313=ORIENTED_EDGE('',*,*,#3078,.T.);
#3323=ORIENTED_EDGE('',*,*,#3157,.T.);
#3336=CARTESIAN_POINT('',(0.127,-6.96,0.0));
#3335=VERTEX_POINT('',#3336);
#3334=EDGE_CURVE('',#3335,#3148,#3339,.T.);
#3339=LINE('',#3336,#3341);
#3341=VECTOR('',#3342,0.381);
#3342=DIRECTION('',(0.0,-1.0,0.0));
#3333=ORIENTED_EDGE('',*,*,#3334,.F.);
#3346=CARTESIAN_POINT('',(0.127,-6.96,2.794));
#3345=VERTEX_POINT('',#3346);
#3344=EDGE_CURVE('',#3345,#3335,#3349,.T.);
#3349=LINE('',#3346,#3351);
#3351=VECTOR('',#3352,2.794);
#3352=DIRECTION('',(0.0,0.0,-1.0));
#3343=ORIENTED_EDGE('',*,*,#3344,.F.);
#3356=CARTESIAN_POINT('',(0.127,-8.28,2.794));
#3355=VERTEX_POINT('',#3356);
#3354=EDGE_CURVE('',#3355,#3345,#3359,.T.);
#3359=LINE('',#3356,#3361);
#3361=VECTOR('',#3362,1.3208);
#3362=DIRECTION('',(0.0,1.0,0.0));
#3353=ORIENTED_EDGE('',*,*,#3354,.F.);
#3366=CARTESIAN_POINT('',(0.127,-8.28,0.0));
#3365=VERTEX_POINT('',#3366);
#3364=EDGE_CURVE('',#3365,#3355,#3369,.T.);
#3369=LINE('',#3366,#3371);
#3371=VECTOR('',#3372,2.794);
#3372=DIRECTION('',(0.0,0.0,1.0));
#3363=ORIENTED_EDGE('',*,*,#3364,.F.);
#3374=EDGE_CURVE('',#3276,#3365,#3379,.T.);
#3379=LINE('',#3277,#3381);
#3381=VECTOR('',#3382,0.381);
#3382=DIRECTION('',(0.0,-1.0,0.0));
#3373=ORIENTED_EDGE('',*,*,#3374,.F.);
#3383=ORIENTED_EDGE('',*,*,#3275,.T.);
#3393=STYLED_ITEM('',(#34),#3394);
#3394=ADVANCED_FACE('',(#3400),#3395,.T.);
#3395=PLANE('',#3396);
#3396=AXIS2_PLACEMENT_3D('',#3397,#3398,#3399);
#3397=CARTESIAN_POINT('',(0.127,-7.341,0.0));
#3398=DIRECTION('',(0.0,0.0,-1.0));
#3399=DIRECTION('',(0.,1.,0.));
#3400=FACE_OUTER_BOUND('',#3401,.T.);
#3401=EDGE_LOOP('',(#3402,#3412,#3422,#3432));
#3402=ORIENTED_EDGE('',*,*,#3147,.T.);
#3412=ORIENTED_EDGE('',*,*,#3236,.T.);
#3423=EDGE_CURVE('',#3335,#3227,#3428,.T.);
#3428=LINE('',#3336,#3430);
#3430=VECTOR('',#3431,0.254);
#3431=DIRECTION('',(-1.0,0.0,0.0));
#3422=ORIENTED_EDGE('',*,*,#3423,.F.);
#3432=ORIENTED_EDGE('',*,*,#3334,.T.);
#3442=STYLED_ITEM('',(#34),#3443);
#3443=ADVANCED_FACE('',(#3449),#3444,.T.);
#3444=PLANE('',#3445);
#3445=AXIS2_PLACEMENT_3D('',#3446,#3447,#3448);
#3446=CARTESIAN_POINT('',(0.127,-8.28,0.0));
#3447=DIRECTION('',(0.0,0.0,-1.0));
#3448=DIRECTION('',(0.,1.,0.));
#3449=FACE_OUTER_BOUND('',#3450,.T.);
#3450=EDGE_LOOP('',(#3451,#3461,#3471,#3481));
#3452=EDGE_CURVE('',#3197,#3365,#3457,.T.);
#3457=LINE('',#3198,#3459);
#3459=VECTOR('',#3460,0.254);
#3460=DIRECTION('',(1.0,0.0,0.0));
#3451=ORIENTED_EDGE('',*,*,#3452,.F.);
#3461=ORIENTED_EDGE('',*,*,#3196,.T.);
#3471=ORIENTED_EDGE('',*,*,#3285,.T.);
#3481=ORIENTED_EDGE('',*,*,#3374,.T.);
#3491=STYLED_ITEM('',(#34),#3492);
#3492=ADVANCED_FACE('',(#3498),#3493,.T.);
#3493=PLANE('',#3494);
#3494=AXIS2_PLACEMENT_3D('',#3495,#3496,#3497);
#3495=CARTESIAN_POINT('',(0.127,-6.96,0.0));
#3496=DIRECTION('',(0.0,1.0,0.0));
#3497=DIRECTION('',(0.,0.,1.));
#3498=FACE_OUTER_BOUND('',#3499,.T.);
#3499=EDGE_LOOP('',(#3500,#3510,#3520,#3530,#3540,#3550));
#3500=ORIENTED_EDGE('',*,*,#3423,.T.);
#3510=ORIENTED_EDGE('',*,*,#3226,.T.);
#3523=CARTESIAN_POINT('',(0.635,-6.96,3.048));
#3522=VERTEX_POINT('',#3523);
#3521=EDGE_CURVE('',#3522,#3217,#3526,.T.);
#3526=LINE('',#3523,#3528);
#3528=VECTOR('',#3529,0.762);
#3529=DIRECTION('',(-1.0,0.0,0.0));
#3520=ORIENTED_EDGE('',*,*,#3521,.F.);
#3533=CARTESIAN_POINT('',(0.635,-6.96,2.794));
#3532=VERTEX_POINT('',#3533);
#3531=EDGE_CURVE('',#3532,#3522,#3536,.T.);
#3536=LINE('',#3533,#3538);
#3538=VECTOR('',#3539,0.254);
#3539=DIRECTION('',(0.0,0.0,1.0));
#3530=ORIENTED_EDGE('',*,*,#3531,.F.);
#3541=EDGE_CURVE('',#3345,#3532,#3546,.T.);
#3546=LINE('',#3346,#3548);
#3548=VECTOR('',#3549,0.508);
#3549=DIRECTION('',(1.0,0.0,0.0));
#3540=ORIENTED_EDGE('',*,*,#3541,.F.);
#3550=ORIENTED_EDGE('',*,*,#3344,.T.);
#3560=STYLED_ITEM('',(#34),#3561);
#3561=ADVANCED_FACE('',(#3567),#3562,.T.);
#3562=PLANE('',#3563);
#3563=AXIS2_PLACEMENT_3D('',#3564,#3565,#3566);
#3564=CARTESIAN_POINT('',(-0.127,-8.28,0.0));
#3565=DIRECTION('',(0.0,-1.0,0.0));
#3566=DIRECTION('',(0.,0.,1.));
#3567=FACE_OUTER_BOUND('',#3568,.T.);
#3568=EDGE_LOOP('',(#3569,#3579,#3589,#3599,#3609,#3619));
#3569=ORIENTED_EDGE('',*,*,#3452,.T.);
#3579=ORIENTED_EDGE('',*,*,#3364,.T.);
#3592=CARTESIAN_POINT('',(0.635,-8.28,2.794));
#3591=VERTEX_POINT('',#3592);
#3590=EDGE_CURVE('',#3591,#3355,#3595,.T.);
#3595=LINE('',#3592,#3597);
#3597=VECTOR('',#3598,0.508);
#3598=DIRECTION('',(-1.0,0.0,0.0));
#3589=ORIENTED_EDGE('',*,*,#3590,.F.);
#3602=CARTESIAN_POINT('',(0.635,-8.28,3.048));
#3601=VERTEX_POINT('',#3602);
#3600=EDGE_CURVE('',#3601,#3591,#3605,.T.);
#3605=LINE('',#3602,#3607);
#3607=VECTOR('',#3608,0.254);
#3608=DIRECTION('',(0.0,0.0,-1.0));
#3599=ORIENTED_EDGE('',*,*,#3600,.F.);
#3610=EDGE_CURVE('',#3207,#3601,#3615,.T.);
#3615=LINE('',#3208,#3617);
#3617=VECTOR('',#3618,0.762);
#3618=DIRECTION('',(1.0,0.0,0.0));
#3609=ORIENTED_EDGE('',*,*,#3610,.F.);
#3619=ORIENTED_EDGE('',*,*,#3206,.T.);
#3629=STYLED_ITEM('',(#34),#3630);
#3630=ADVANCED_FACE('',(#3636),#3631,.T.);
#3631=PLANE('',#3632);
#3632=AXIS2_PLACEMENT_3D('',#3633,#3634,#3635);
#3633=CARTESIAN_POINT('',(0.635,-8.28,2.794));
#3634=DIRECTION('',(0.0,0.0,-1.0));
#3635=DIRECTION('',(0.,1.,0.));
#3636=FACE_OUTER_BOUND('',#3637,.T.);
#3637=EDGE_LOOP('',(#3638,#3648,#3658,#3668));
#3638=ORIENTED_EDGE('',*,*,#3590,.T.);
#3648=ORIENTED_EDGE('',*,*,#3354,.T.);
#3658=ORIENTED_EDGE('',*,*,#3541,.T.);
#3669=EDGE_CURVE('',#3591,#3532,#3674,.T.);
#3674=LINE('',#3592,#3676);
#3676=VECTOR('',#3677,1.3208);
#3677=DIRECTION('',(0.0,1.0,0.0));
#3668=ORIENTED_EDGE('',*,*,#3669,.F.);
#3678=STYLED_ITEM('',(#34),#3679);
#3679=ADVANCED_FACE('',(#3685),#3680,.T.);
#3680=PLANE('',#3681);
#3681=AXIS2_PLACEMENT_3D('',#3682,#3683,#3684);
#3682=CARTESIAN_POINT('',(0.635,-8.28,2.794));
#3683=DIRECTION('',(1.0,0.0,0.0));
#3684=DIRECTION('',(0.,0.,1.));
#3685=FACE_OUTER_BOUND('',#3686,.T.);
#3686=EDGE_LOOP('',(#3687,#3697,#3707,#3717));
#3687=ORIENTED_EDGE('',*,*,#3669,.T.);
#3697=ORIENTED_EDGE('',*,*,#3531,.T.);
#3708=EDGE_CURVE('',#3601,#3522,#3713,.T.);
#3713=LINE('',#3602,#3715);
#3715=VECTOR('',#3716,1.3208);
#3716=DIRECTION('',(0.0,1.0,0.0));
#3707=ORIENTED_EDGE('',*,*,#3708,.F.);
#3717=ORIENTED_EDGE('',*,*,#3600,.T.);
#3727=STYLED_ITEM('',(#34),#3728);
#3728=ADVANCED_FACE('',(#3734),#3729,.T.);
#3729=PLANE('',#3730);
#3730=AXIS2_PLACEMENT_3D('',#3731,#3732,#3733);
#3731=CARTESIAN_POINT('',(0.635,-6.96,3.048));
#3732=DIRECTION('',(0.0,0.0,1.0));
#3733=DIRECTION('',(0.,1.,0.));
#3734=FACE_OUTER_BOUND('',#3735,.T.);
#3735=EDGE_LOOP('',(#3736,#3746,#3756,#3766));
#3736=ORIENTED_EDGE('',*,*,#3521,.T.);
#3746=ORIENTED_EDGE('',*,*,#3216,.T.);
#3756=ORIENTED_EDGE('',*,*,#3610,.T.);
#3766=ORIENTED_EDGE('',*,*,#3708,.T.);
#3777=MANIFOLD_SOLID_BREP($,#3778);
#3778=CLOSED_SHELL('',(#3780,#3829,#3878,#3967,#4016,#4105,#4154,#4203,#4272,#4341,#4390,#4439));
#3779=STYLED_ITEM('',(#34),#3780);
#3780=ADVANCED_FACE('',(#3786),#3781,.T.);
#3781=PLANE('',#3782);
#3782=AXIS2_PLACEMENT_3D('',#3783,#3784,#3785);
#3783=CARTESIAN_POINT('',(7.747,-7.899,-2.54));
#3784=DIRECTION('',(0.0,0.0,-1.0));
#3785=DIRECTION('',(0.,1.,0.));
#3786=FACE_OUTER_BOUND('',#3787,.T.);
#3787=EDGE_LOOP('',(#3788,#3798,#3808,#3818));
#3791=CARTESIAN_POINT('',(7.747,-7.341,-2.54));
#3790=VERTEX_POINT('',#3791);
#3793=CARTESIAN_POINT('',(7.747,-7.899,-2.54));
#3792=VERTEX_POINT('',#3793);
#3789=EDGE_CURVE('',#3790,#3792,#3794,.T.);
#3794=LINE('',#3791,#3796);
#3796=VECTOR('',#3797,0.5588);
#3797=DIRECTION('',(0.0,-1.0,0.0));
#3788=ORIENTED_EDGE('',*,*,#3789,.F.);
#3801=CARTESIAN_POINT('',(8.001,-7.341,-2.54));
#3800=VERTEX_POINT('',#3801);
#3799=EDGE_CURVE('',#3800,#3790,#3804,.T.);
#3804=LINE('',#3801,#3806);
#3806=VECTOR('',#3807,0.254);
#3807=DIRECTION('',(-1.0,0.0,0.0));
#3798=ORIENTED_EDGE('',*,*,#3799,.F.);
#3811=CARTESIAN_POINT('',(8.001,-7.899,-2.54));
#3810=VERTEX_POINT('',#3811);
#3809=EDGE_CURVE('',#3810,#3800,#3814,.T.);
#3814=LINE('',#3811,#3816);
#3816=VECTOR('',#3817,0.5588);
#3817=DIRECTION('',(0.0,1.0,0.0));
#3808=ORIENTED_EDGE('',*,*,#3809,.F.);
#3819=EDGE_CURVE('',#3792,#3810,#3824,.T.);
#3824=LINE('',#3793,#3826);
#3826=VECTOR('',#3827,0.254);
#3827=DIRECTION('',(1.0,0.0,0.0));
#3818=ORIENTED_EDGE('',*,*,#3819,.F.);
#3828=STYLED_ITEM('',(#34),#3829);
#3829=ADVANCED_FACE('',(#3835),#3830,.T.);
#3830=PLANE('',#3831);
#3831=AXIS2_PLACEMENT_3D('',#3832,#3833,#3834);
#3832=CARTESIAN_POINT('',(7.747,-7.899,-2.54));
#3833=DIRECTION('',(0.0,-1.0,0.0));
#3834=DIRECTION('',(0.,0.,1.));
#3835=FACE_OUTER_BOUND('',#3836,.T.);
#3836=EDGE_LOOP('',(#3837,#3847,#3857,#3867));
#3837=ORIENTED_EDGE('',*,*,#3819,.T.);
#3850=CARTESIAN_POINT('',(8.001,-7.899,0.0));
#3849=VERTEX_POINT('',#3850);
#3848=EDGE_CURVE('',#3849,#3810,#3853,.T.);
#3853=LINE('',#3850,#3855);
#3855=VECTOR('',#3856,2.54);
#3856=DIRECTION('',(0.0,0.0,-1.0));
#3847=ORIENTED_EDGE('',*,*,#3848,.F.);
#3860=CARTESIAN_POINT('',(7.747,-7.899,0.0));
#3859=VERTEX_POINT('',#3860);
#3858=EDGE_CURVE('',#3859,#3849,#3863,.T.);
#3863=LINE('',#3860,#3865);
#3865=VECTOR('',#3866,0.254);
#3866=DIRECTION('',(1.0,0.0,0.0));
#3857=ORIENTED_EDGE('',*,*,#3858,.F.);
#3868=EDGE_CURVE('',#3792,#3859,#3873,.T.);
#3873=LINE('',#3793,#3875);
#3875=VECTOR('',#3876,2.54);
#3876=DIRECTION('',(0.0,0.0,1.0));
#3867=ORIENTED_EDGE('',*,*,#3868,.F.);
#3877=STYLED_ITEM('',(#34),#3878);
#3878=ADVANCED_FACE('',(#3884),#3879,.T.);
#3879=PLANE('',#3880);
#3880=AXIS2_PLACEMENT_3D('',#3881,#3882,#3883);
#3881=CARTESIAN_POINT('',(8.001,-7.899,-2.54));
#3882=DIRECTION('',(1.0,0.0,0.0));
#3883=DIRECTION('',(0.,0.,1.));
#3884=FACE_OUTER_BOUND('',#3885,.T.);
#3885=EDGE_LOOP('',(#3886,#3896,#3906,#3916,#3926,#3936,#3946,#3956));
#3886=ORIENTED_EDGE('',*,*,#3809,.T.);
#3899=CARTESIAN_POINT('',(8.001,-7.341,0.0));
#3898=VERTEX_POINT('',#3899);
#3897=EDGE_CURVE('',#3898,#3800,#3902,.T.);
#3902=LINE('',#3899,#3904);
#3904=VECTOR('',#3905,2.54);
#3905=DIRECTION('',(0.0,0.0,-1.0));
#3896=ORIENTED_EDGE('',*,*,#3897,.F.);
#3909=CARTESIAN_POINT('',(8.001,-6.96,0.0));
#3908=VERTEX_POINT('',#3909);
#3907=EDGE_CURVE('',#3908,#3898,#3912,.T.);
#3912=LINE('',#3909,#3914);
#3914=VECTOR('',#3915,0.381);
#3915=DIRECTION('',(0.0,-1.0,0.0));
#3906=ORIENTED_EDGE('',*,*,#3907,.F.);
#3919=CARTESIAN_POINT('',(8.001,-6.96,3.048));
#3918=VERTEX_POINT('',#3919);
#3917=EDGE_CURVE('',#3918,#3908,#3922,.T.);
#3922=LINE('',#3919,#3924);
#3924=VECTOR('',#3925,3.048);
#3925=DIRECTION('',(0.0,0.0,-1.0));
#3916=ORIENTED_EDGE('',*,*,#3917,.F.);
#3929=CARTESIAN_POINT('',(8.001,-8.28,3.048));
#3928=VERTEX_POINT('',#3929);
#3927=EDGE_CURVE('',#3928,#3918,#3932,.T.);
#3932=LINE('',#3929,#3934);
#3934=VECTOR('',#3935,1.3208);
#3935=DIRECTION('',(0.0,1.0,0.0));
#3926=ORIENTED_EDGE('',*,*,#3927,.F.);
#3939=CARTESIAN_POINT('',(8.001,-8.28,0.0));
#3938=VERTEX_POINT('',#3939);
#3937=EDGE_CURVE('',#3938,#3928,#3942,.T.);
#3942=LINE('',#3939,#3944);
#3944=VECTOR('',#3945,3.048);
#3945=DIRECTION('',(0.0,0.0,1.0));
#3936=ORIENTED_EDGE('',*,*,#3937,.F.);
#3947=EDGE_CURVE('',#3849,#3938,#3952,.T.);
#3952=LINE('',#3850,#3954);
#3954=VECTOR('',#3955,0.381);
#3955=DIRECTION('',(0.0,-1.0,0.0));
#3946=ORIENTED_EDGE('',*,*,#3947,.F.);
#3956=ORIENTED_EDGE('',*,*,#3848,.T.);
#3966=STYLED_ITEM('',(#34),#3967);
#3967=ADVANCED_FACE('',(#3973),#3968,.T.);
#3968=PLANE('',#3969);
#3969=AXIS2_PLACEMENT_3D('',#3970,#3971,#3972);
#3970=CARTESIAN_POINT('',(8.001,-7.341,-2.54));
#3971=DIRECTION('',(0.0,1.0,0.0));
#3972=DIRECTION('',(0.,0.,1.));
#3973=FACE_OUTER_BOUND('',#3974,.T.);
#3974=EDGE_LOOP('',(#3975,#3985,#3995,#4005));
#3975=ORIENTED_EDGE('',*,*,#3799,.T.);
#3988=CARTESIAN_POINT('',(7.747,-7.341,0.0));
#3987=VERTEX_POINT('',#3988);
#3986=EDGE_CURVE('',#3987,#3790,#3991,.T.);
#3991=LINE('',#3988,#3993);
#3993=VECTOR('',#3994,2.54);
#3994=DIRECTION('',(0.0,0.0,-1.0));
#3985=ORIENTED_EDGE('',*,*,#3986,.F.);
#3996=EDGE_CURVE('',#3898,#3987,#4001,.T.);
#4001=LINE('',#3899,#4003);
#4003=VECTOR('',#4004,0.254);
#4004=DIRECTION('',(-1.0,0.0,0.0));
#3995=ORIENTED_EDGE('',*,*,#3996,.F.);
#4005=ORIENTED_EDGE('',*,*,#3897,.T.);
#4015=STYLED_ITEM('',(#34),#4016);
#4016=ADVANCED_FACE('',(#4022),#4017,.T.);
#4017=PLANE('',#4018);
#4018=AXIS2_PLACEMENT_3D('',#4019,#4020,#4021);
#4019=CARTESIAN_POINT('',(7.747,-7.341,-2.54));
#4020=DIRECTION('',(-1.0,0.0,0.0));
#4021=DIRECTION('',(0.,0.,1.));
#4022=FACE_OUTER_BOUND('',#4023,.T.);
#4023=EDGE_LOOP('',(#4024,#4034,#4044,#4054,#4064,#4074,#4084,#4094));
#4024=ORIENTED_EDGE('',*,*,#3789,.T.);
#4034=ORIENTED_EDGE('',*,*,#3868,.T.);
#4047=CARTESIAN_POINT('',(7.747,-8.28,0.0));
#4046=VERTEX_POINT('',#4047);
#4045=EDGE_CURVE('',#4046,#3859,#4050,.T.);
#4050=LINE('',#4047,#4052);
#4052=VECTOR('',#4053,0.381);
#4053=DIRECTION('',(0.0,1.0,0.0));
#4044=ORIENTED_EDGE('',*,*,#4045,.F.);
#4057=CARTESIAN_POINT('',(7.747,-8.28,2.794));
#4056=VERTEX_POINT('',#4057);
#4055=EDGE_CURVE('',#4056,#4046,#4060,.T.);
#4060=LINE('',#4057,#4062);
#4062=VECTOR('',#4063,2.794);
#4063=DIRECTION('',(0.0,0.0,-1.0));
#4054=ORIENTED_EDGE('',*,*,#4055,.F.);
#4067=CARTESIAN_POINT('',(7.747,-6.96,2.794));
#4066=VERTEX_POINT('',#4067);
#4065=EDGE_CURVE('',#4066,#4056,#4070,.T.);
#4070=LINE('',#4067,#4072);
#4072=VECTOR('',#4073,1.3208);
#4073=DIRECTION('',(0.0,-1.0,0.0));
#4064=ORIENTED_EDGE('',*,*,#4065,.F.);
#4077=CARTESIAN_POINT('',(7.747,-6.96,0.0));
#4076=VERTEX_POINT('',#4077);
#4075=EDGE_CURVE('',#4076,#4066,#4080,.T.);
#4080=LINE('',#4077,#4082);
#4082=VECTOR('',#4083,2.794);
#4083=DIRECTION('',(0.0,0.0,1.0));
#4074=ORIENTED_EDGE('',*,*,#4075,.F.);
#4085=EDGE_CURVE('',#3987,#4076,#4090,.T.);
#4090=LINE('',#3988,#4092);
#4092=VECTOR('',#4093,0.381);
#4093=DIRECTION('',(0.0,1.0,0.0));
#4084=ORIENTED_EDGE('',*,*,#4085,.F.);
#4094=ORIENTED_EDGE('',*,*,#3986,.T.);
#4104=STYLED_ITEM('',(#34),#4105);
#4105=ADVANCED_FACE('',(#4111),#4106,.T.);
#4106=PLANE('',#4107);
#4107=AXIS2_PLACEMENT_3D('',#4108,#4109,#4110);
#4108=CARTESIAN_POINT('',(7.747,-7.899,0.0));
#4109=DIRECTION('',(0.0,0.0,-1.0));
#4110=DIRECTION('',(0.,1.,0.));
#4111=FACE_OUTER_BOUND('',#4112,.T.);
#4112=EDGE_LOOP('',(#4113,#4123,#4133,#4143));
#4113=ORIENTED_EDGE('',*,*,#3858,.T.);
#4123=ORIENTED_EDGE('',*,*,#3947,.T.);
#4134=EDGE_CURVE('',#4046,#3938,#4139,.T.);
#4139=LINE('',#4047,#4141);
#4141=VECTOR('',#4142,0.254);
#4142=DIRECTION('',(1.0,0.0,0.0));
#4133=ORIENTED_EDGE('',*,*,#4134,.F.);
#4143=ORIENTED_EDGE('',*,*,#4045,.T.);
#4153=STYLED_ITEM('',(#34),#4154);
#4154=ADVANCED_FACE('',(#4160),#4155,.T.);
#4155=PLANE('',#4156);
#4156=AXIS2_PLACEMENT_3D('',#4157,#4158,#4159);
#4157=CARTESIAN_POINT('',(7.747,-6.96,0.0));
#4158=DIRECTION('',(0.0,0.0,-1.0));
#4159=DIRECTION('',(0.,1.,0.));
#4160=FACE_OUTER_BOUND('',#4161,.T.);
#4161=EDGE_LOOP('',(#4162,#4172,#4182,#4192));
#4163=EDGE_CURVE('',#3908,#4076,#4168,.T.);
#4168=LINE('',#3909,#4170);
#4170=VECTOR('',#4171,0.254);
#4171=DIRECTION('',(-1.0,0.0,0.0));
#4162=ORIENTED_EDGE('',*,*,#4163,.F.);
#4172=ORIENTED_EDGE('',*,*,#3907,.T.);
#4182=ORIENTED_EDGE('',*,*,#3996,.T.);
#4192=ORIENTED_EDGE('',*,*,#4085,.T.);
#4202=STYLED_ITEM('',(#34),#4203);
#4203=ADVANCED_FACE('',(#4209),#4204,.T.);
#4204=PLANE('',#4205);
#4205=AXIS2_PLACEMENT_3D('',#4206,#4207,#4208);
#4206=CARTESIAN_POINT('',(7.747,-8.28,0.0));
#4207=DIRECTION('',(0.0,-1.0,0.0));
#4208=DIRECTION('',(0.,0.,1.));
#4209=FACE_OUTER_BOUND('',#4210,.T.);
#4210=EDGE_LOOP('',(#4211,#4221,#4231,#4241,#4251,#4261));
#4211=ORIENTED_EDGE('',*,*,#4134,.T.);
#4221=ORIENTED_EDGE('',*,*,#3937,.T.);
#4234=CARTESIAN_POINT('',(7.239,-8.28,3.048));
#4233=VERTEX_POINT('',#4234);
#4232=EDGE_CURVE('',#4233,#3928,#4237,.T.);
#4237=LINE('',#4234,#4239);
#4239=VECTOR('',#4240,0.762);
#4240=DIRECTION('',(1.0,0.0,0.0));
#4231=ORIENTED_EDGE('',*,*,#4232,.F.);
#4244=CARTESIAN_POINT('',(7.239,-8.28,2.794));
#4243=VERTEX_POINT('',#4244);
#4242=EDGE_CURVE('',#4243,#4233,#4247,.T.);
#4247=LINE('',#4244,#4249);
#4249=VECTOR('',#4250,0.254);
#4250=DIRECTION('',(0.0,0.0,1.0));
#4241=ORIENTED_EDGE('',*,*,#4242,.F.);
#4252=EDGE_CURVE('',#4056,#4243,#4257,.T.);
#4257=LINE('',#4057,#4259);
#4259=VECTOR('',#4260,0.508);
#4260=DIRECTION('',(-1.0,0.0,0.0));
#4251=ORIENTED_EDGE('',*,*,#4252,.F.);
#4261=ORIENTED_EDGE('',*,*,#4055,.T.);
#4271=STYLED_ITEM('',(#34),#4272);
#4272=ADVANCED_FACE('',(#4278),#4273,.T.);
#4273=PLANE('',#4274);
#4274=AXIS2_PLACEMENT_3D('',#4275,#4276,#4277);
#4275=CARTESIAN_POINT('',(8.001,-6.96,0.0));
#4276=DIRECTION('',(0.0,1.0,0.0));
#4277=DIRECTION('',(0.,0.,1.));
#4278=FACE_OUTER_BOUND('',#4279,.T.);
#4279=EDGE_LOOP('',(#4280,#4290,#4300,#4310,#4320,#4330));
#4280=ORIENTED_EDGE('',*,*,#4163,.T.);
#4290=ORIENTED_EDGE('',*,*,#4075,.T.);
#4303=CARTESIAN_POINT('',(7.239,-6.96,2.794));
#4302=VERTEX_POINT('',#4303);
#4301=EDGE_CURVE('',#4302,#4066,#4306,.T.);
#4306=LINE('',#4303,#4308);
#4308=VECTOR('',#4309,0.508);
#4309=DIRECTION('',(1.0,0.0,0.0));
#4300=ORIENTED_EDGE('',*,*,#4301,.F.);
#4313=CARTESIAN_POINT('',(7.239,-6.96,3.048));
#4312=VERTEX_POINT('',#4313);
#4311=EDGE_CURVE('',#4312,#4302,#4316,.T.);
#4316=LINE('',#4313,#4318);
#4318=VECTOR('',#4319,0.254);
#4319=DIRECTION('',(0.0,0.0,-1.0));
#4310=ORIENTED_EDGE('',*,*,#4311,.F.);
#4321=EDGE_CURVE('',#3918,#4312,#4326,.T.);
#4326=LINE('',#3919,#4328);
#4328=VECTOR('',#4329,0.762);
#4329=DIRECTION('',(-1.0,0.0,0.0));
#4320=ORIENTED_EDGE('',*,*,#4321,.F.);
#4330=ORIENTED_EDGE('',*,*,#3917,.T.);
#4340=STYLED_ITEM('',(#34),#4341);
#4341=ADVANCED_FACE('',(#4347),#4342,.T.);
#4342=PLANE('',#4343);
#4343=AXIS2_PLACEMENT_3D('',#4344,#4345,#4346);
#4344=CARTESIAN_POINT('',(7.239,-6.96,2.794));
#4345=DIRECTION('',(0.0,0.0,-1.0));
#4346=DIRECTION('',(0.,1.,0.));
#4347=FACE_OUTER_BOUND('',#4348,.T.);
#4348=EDGE_LOOP('',(#4349,#4359,#4369,#4379));
#4349=ORIENTED_EDGE('',*,*,#4301,.T.);
#4359=ORIENTED_EDGE('',*,*,#4065,.T.);
#4369=ORIENTED_EDGE('',*,*,#4252,.T.);
#4380=EDGE_CURVE('',#4302,#4243,#4385,.T.);
#4385=LINE('',#4303,#4387);
#4387=VECTOR('',#4388,1.3208);
#4388=DIRECTION('',(0.0,-1.0,0.0));
#4379=ORIENTED_EDGE('',*,*,#4380,.F.);
#4389=STYLED_ITEM('',(#34),#4390);
#4390=ADVANCED_FACE('',(#4396),#4391,.T.);
#4391=PLANE('',#4392);
#4392=AXIS2_PLACEMENT_3D('',#4393,#4394,#4395);
#4393=CARTESIAN_POINT('',(7.239,-6.96,2.794));
#4394=DIRECTION('',(-1.0,0.0,0.0));
#4395=DIRECTION('',(0.,0.,1.));
#4396=FACE_OUTER_BOUND('',#4397,.T.);
#4397=EDGE_LOOP('',(#4398,#4408,#4418,#4428));
#4398=ORIENTED_EDGE('',*,*,#4380,.T.);
#4408=ORIENTED_EDGE('',*,*,#4242,.T.);
#4419=EDGE_CURVE('',#4312,#4233,#4424,.T.);
#4424=LINE('',#4313,#4426);
#4426=VECTOR('',#4427,1.3208);
#4427=DIRECTION('',(0.0,-1.0,0.0));
#4418=ORIENTED_EDGE('',*,*,#4419,.F.);
#4428=ORIENTED_EDGE('',*,*,#4311,.T.);
#4438=STYLED_ITEM('',(#34),#4439);
#4439=ADVANCED_FACE('',(#4445),#4440,.T.);
#4440=PLANE('',#4441);
#4441=AXIS2_PLACEMENT_3D('',#4442,#4443,#4444);
#4442=CARTESIAN_POINT('',(7.239,-8.28,3.048));
#4443=DIRECTION('',(0.0,0.0,1.0));
#4444=DIRECTION('',(0.,1.,0.));
#4445=FACE_OUTER_BOUND('',#4446,.T.);
#4446=EDGE_LOOP('',(#4447,#4457,#4467,#4477));
#4447=ORIENTED_EDGE('',*,*,#4232,.T.);
#4457=ORIENTED_EDGE('',*,*,#3927,.T.);
#4467=ORIENTED_EDGE('',*,*,#4321,.T.);
#4477=ORIENTED_EDGE('',*,*,#4419,.T.);
#4488=MANIFOLD_SOLID_BREP($,#4489);
#4489=CLOSED_SHELL('',(#4491,#4540,#4589,#4678,#4727,#4816,#4865,#4914,#4983,#5052,#5101,#5150));
#4490=STYLED_ITEM('',(#34),#4491);
#4491=ADVANCED_FACE('',(#4497),#4492,.T.);
#4492=PLANE('',#4493);
#4493=AXIS2_PLACEMENT_3D('',#4494,#4495,#4496);
#4494=CARTESIAN_POINT('',(7.747,-5.359,-2.54));
#4495=DIRECTION('',(0.0,0.0,-1.0));
#4496=DIRECTION('',(0.,1.,0.));
#4497=FACE_OUTER_BOUND('',#4498,.T.);
#4498=EDGE_LOOP('',(#4499,#4509,#4519,#4529));
#4502=CARTESIAN_POINT('',(7.747,-4.801,-2.54));
#4501=VERTEX_POINT('',#4502);
#4504=CARTESIAN_POINT('',(7.747,-5.359,-2.54));
#4503=VERTEX_POINT('',#4504);
#4500=EDGE_CURVE('',#4501,#4503,#4505,.T.);
#4505=LINE('',#4502,#4507);
#4507=VECTOR('',#4508,0.5588);
#4508=DIRECTION('',(0.0,-1.0,0.0));
#4499=ORIENTED_EDGE('',*,*,#4500,.F.);
#4512=CARTESIAN_POINT('',(8.001,-4.801,-2.54));
#4511=VERTEX_POINT('',#4512);
#4510=EDGE_CURVE('',#4511,#4501,#4515,.T.);
#4515=LINE('',#4512,#4517);
#4517=VECTOR('',#4518,0.254);
#4518=DIRECTION('',(-1.0,0.0,0.0));
#4509=ORIENTED_EDGE('',*,*,#4510,.F.);
#4522=CARTESIAN_POINT('',(8.001,-5.359,-2.54));
#4521=VERTEX_POINT('',#4522);
#4520=EDGE_CURVE('',#4521,#4511,#4525,.T.);
#4525=LINE('',#4522,#4527);
#4527=VECTOR('',#4528,0.5588);
#4528=DIRECTION('',(0.0,1.0,0.0));
#4519=ORIENTED_EDGE('',*,*,#4520,.F.);
#4530=EDGE_CURVE('',#4503,#4521,#4535,.T.);
#4535=LINE('',#4504,#4537);
#4537=VECTOR('',#4538,0.254);
#4538=DIRECTION('',(1.0,0.0,0.0));
#4529=ORIENTED_EDGE('',*,*,#4530,.F.);
#4539=STYLED_ITEM('',(#34),#4540);
#4540=ADVANCED_FACE('',(#4546),#4541,.T.);
#4541=PLANE('',#4542);
#4542=AXIS2_PLACEMENT_3D('',#4543,#4544,#4545);
#4543=CARTESIAN_POINT('',(7.747,-5.359,-2.54));
#4544=DIRECTION('',(0.0,-1.0,0.0));
#4545=DIRECTION('',(0.,0.,1.));
#4546=FACE_OUTER_BOUND('',#4547,.T.);
#4547=EDGE_LOOP('',(#4548,#4558,#4568,#4578));
#4548=ORIENTED_EDGE('',*,*,#4530,.T.);
#4561=CARTESIAN_POINT('',(8.001,-5.359,0.0));
#4560=VERTEX_POINT('',#4561);
#4559=EDGE_CURVE('',#4560,#4521,#4564,.T.);
#4564=LINE('',#4561,#4566);
#4566=VECTOR('',#4567,2.54);
#4567=DIRECTION('',(0.0,0.0,-1.0));
#4558=ORIENTED_EDGE('',*,*,#4559,.F.);
#4571=CARTESIAN_POINT('',(7.747,-5.359,0.0));
#4570=VERTEX_POINT('',#4571);
#4569=EDGE_CURVE('',#4570,#4560,#4574,.T.);
#4574=LINE('',#4571,#4576);
#4576=VECTOR('',#4577,0.254);
#4577=DIRECTION('',(1.0,0.0,0.0));
#4568=ORIENTED_EDGE('',*,*,#4569,.F.);
#4579=EDGE_CURVE('',#4503,#4570,#4584,.T.);
#4584=LINE('',#4504,#4586);
#4586=VECTOR('',#4587,2.54);
#4587=DIRECTION('',(0.0,0.0,1.0));
#4578=ORIENTED_EDGE('',*,*,#4579,.F.);
#4588=STYLED_ITEM('',(#34),#4589);
#4589=ADVANCED_FACE('',(#4595),#4590,.T.);
#4590=PLANE('',#4591);
#4591=AXIS2_PLACEMENT_3D('',#4592,#4593,#4594);
#4592=CARTESIAN_POINT('',(8.001,-5.359,-2.54));
#4593=DIRECTION('',(1.0,0.0,0.0));
#4594=DIRECTION('',(0.,0.,1.));
#4595=FACE_OUTER_BOUND('',#4596,.T.);
#4596=EDGE_LOOP('',(#4597,#4607,#4617,#4627,#4637,#4647,#4657,#4667));
#4597=ORIENTED_EDGE('',*,*,#4520,.T.);
#4610=CARTESIAN_POINT('',(8.001,-4.801,0.0));
#4609=VERTEX_POINT('',#4610);
#4608=EDGE_CURVE('',#4609,#4511,#4613,.T.);
#4613=LINE('',#4610,#4615);
#4615=VECTOR('',#4616,2.54);
#4616=DIRECTION('',(0.0,0.0,-1.0));
#4607=ORIENTED_EDGE('',*,*,#4608,.F.);
#4620=CARTESIAN_POINT('',(8.001,-4.42,0.0));
#4619=VERTEX_POINT('',#4620);
#4618=EDGE_CURVE('',#4619,#4609,#4623,.T.);
#4623=LINE('',#4620,#4625);
#4625=VECTOR('',#4626,0.381);
#4626=DIRECTION('',(0.0,-1.0,0.0));
#4617=ORIENTED_EDGE('',*,*,#4618,.F.);
#4630=CARTESIAN_POINT('',(8.001,-4.42,3.048));
#4629=VERTEX_POINT('',#4630);
#4628=EDGE_CURVE('',#4629,#4619,#4633,.T.);
#4633=LINE('',#4630,#4635);
#4635=VECTOR('',#4636,3.048);
#4636=DIRECTION('',(0.0,0.0,-1.0));
#4627=ORIENTED_EDGE('',*,*,#4628,.F.);
#4640=CARTESIAN_POINT('',(8.001,-5.74,3.048));
#4639=VERTEX_POINT('',#4640);
#4638=EDGE_CURVE('',#4639,#4629,#4643,.T.);
#4643=LINE('',#4640,#4645);
#4645=VECTOR('',#4646,1.3208);
#4646=DIRECTION('',(0.0,1.0,0.0));
#4637=ORIENTED_EDGE('',*,*,#4638,.F.);
#4650=CARTESIAN_POINT('',(8.001,-5.74,0.0));
#4649=VERTEX_POINT('',#4650);
#4648=EDGE_CURVE('',#4649,#4639,#4653,.T.);
#4653=LINE('',#4650,#4655);
#4655=VECTOR('',#4656,3.048);
#4656=DIRECTION('',(0.0,0.0,1.0));
#4647=ORIENTED_EDGE('',*,*,#4648,.F.);
#4658=EDGE_CURVE('',#4560,#4649,#4663,.T.);
#4663=LINE('',#4561,#4665);
#4665=VECTOR('',#4666,0.381);
#4666=DIRECTION('',(0.0,-1.0,0.0));
#4657=ORIENTED_EDGE('',*,*,#4658,.F.);
#4667=ORIENTED_EDGE('',*,*,#4559,.T.);
#4677=STYLED_ITEM('',(#34),#4678);
#4678=ADVANCED_FACE('',(#4684),#4679,.T.);
#4679=PLANE('',#4680);
#4680=AXIS2_PLACEMENT_3D('',#4681,#4682,#4683);
#4681=CARTESIAN_POINT('',(8.001,-4.801,-2.54));
#4682=DIRECTION('',(0.0,1.0,0.0));
#4683=DIRECTION('',(0.,0.,1.));
#4684=FACE_OUTER_BOUND('',#4685,.T.);
#4685=EDGE_LOOP('',(#4686,#4696,#4706,#4716));
#4686=ORIENTED_EDGE('',*,*,#4510,.T.);
#4699=CARTESIAN_POINT('',(7.747,-4.801,0.0));
#4698=VERTEX_POINT('',#4699);
#4697=EDGE_CURVE('',#4698,#4501,#4702,.T.);
#4702=LINE('',#4699,#4704);
#4704=VECTOR('',#4705,2.54);
#4705=DIRECTION('',(0.0,0.0,-1.0));
#4696=ORIENTED_EDGE('',*,*,#4697,.F.);
#4707=EDGE_CURVE('',#4609,#4698,#4712,.T.);
#4712=LINE('',#4610,#4714);
#4714=VECTOR('',#4715,0.254);
#4715=DIRECTION('',(-1.0,0.0,0.0));
#4706=ORIENTED_EDGE('',*,*,#4707,.F.);
#4716=ORIENTED_EDGE('',*,*,#4608,.T.);
#4726=STYLED_ITEM('',(#34),#4727);
#4727=ADVANCED_FACE('',(#4733),#4728,.T.);
#4728=PLANE('',#4729);
#4729=AXIS2_PLACEMENT_3D('',#4730,#4731,#4732);
#4730=CARTESIAN_POINT('',(7.747,-4.801,-2.54));
#4731=DIRECTION('',(-1.0,0.0,0.0));
#4732=DIRECTION('',(0.,0.,1.));
#4733=FACE_OUTER_BOUND('',#4734,.T.);
#4734=EDGE_LOOP('',(#4735,#4745,#4755,#4765,#4775,#4785,#4795,#4805));
#4735=ORIENTED_EDGE('',*,*,#4500,.T.);
#4745=ORIENTED_EDGE('',*,*,#4579,.T.);
#4758=CARTESIAN_POINT('',(7.747,-5.74,0.0));
#4757=VERTEX_POINT('',#4758);
#4756=EDGE_CURVE('',#4757,#4570,#4761,.T.);
#4761=LINE('',#4758,#4763);
#4763=VECTOR('',#4764,0.381);
#4764=DIRECTION('',(0.0,1.0,0.0));
#4755=ORIENTED_EDGE('',*,*,#4756,.F.);
#4768=CARTESIAN_POINT('',(7.747,-5.74,2.794));
#4767=VERTEX_POINT('',#4768);
#4766=EDGE_CURVE('',#4767,#4757,#4771,.T.);
#4771=LINE('',#4768,#4773);
#4773=VECTOR('',#4774,2.794);
#4774=DIRECTION('',(0.0,0.0,-1.0));
#4765=ORIENTED_EDGE('',*,*,#4766,.F.);
#4778=CARTESIAN_POINT('',(7.747,-4.42,2.794));
#4777=VERTEX_POINT('',#4778);
#4776=EDGE_CURVE('',#4777,#4767,#4781,.T.);
#4781=LINE('',#4778,#4783);
#4783=VECTOR('',#4784,1.3208);
#4784=DIRECTION('',(0.0,-1.0,0.0));
#4775=ORIENTED_EDGE('',*,*,#4776,.F.);
#4788=CARTESIAN_POINT('',(7.747,-4.42,0.0));
#4787=VERTEX_POINT('',#4788);
#4786=EDGE_CURVE('',#4787,#4777,#4791,.T.);
#4791=LINE('',#4788,#4793);
#4793=VECTOR('',#4794,2.794);
#4794=DIRECTION('',(0.0,0.0,1.0));
#4785=ORIENTED_EDGE('',*,*,#4786,.F.);
#4796=EDGE_CURVE('',#4698,#4787,#4801,.T.);
#4801=LINE('',#4699,#4803);
#4803=VECTOR('',#4804,0.381);
#4804=DIRECTION('',(0.0,1.0,0.0));
#4795=ORIENTED_EDGE('',*,*,#4796,.F.);
#4805=ORIENTED_EDGE('',*,*,#4697,.T.);
#4815=STYLED_ITEM('',(#34),#4816);
#4816=ADVANCED_FACE('',(#4822),#4817,.T.);
#4817=PLANE('',#4818);
#4818=AXIS2_PLACEMENT_3D('',#4819,#4820,#4821);
#4819=CARTESIAN_POINT('',(7.747,-5.359,0.0));
#4820=DIRECTION('',(0.0,0.0,-1.0));
#4821=DIRECTION('',(0.,1.,0.));
#4822=FACE_OUTER_BOUND('',#4823,.T.);
#4823=EDGE_LOOP('',(#4824,#4834,#4844,#4854));
#4824=ORIENTED_EDGE('',*,*,#4569,.T.);
#4834=ORIENTED_EDGE('',*,*,#4658,.T.);
#4845=EDGE_CURVE('',#4757,#4649,#4850,.T.);
#4850=LINE('',#4758,#4852);
#4852=VECTOR('',#4853,0.254);
#4853=DIRECTION('',(1.0,0.0,0.0));
#4844=ORIENTED_EDGE('',*,*,#4845,.F.);
#4854=ORIENTED_EDGE('',*,*,#4756,.T.);
#4864=STYLED_ITEM('',(#34),#4865);
#4865=ADVANCED_FACE('',(#4871),#4866,.T.);
#4866=PLANE('',#4867);
#4867=AXIS2_PLACEMENT_3D('',#4868,#4869,#4870);
#4868=CARTESIAN_POINT('',(7.747,-4.42,0.0));
#4869=DIRECTION('',(0.0,0.0,-1.0));
#4870=DIRECTION('',(0.,1.,0.));
#4871=FACE_OUTER_BOUND('',#4872,.T.);
#4872=EDGE_LOOP('',(#4873,#4883,#4893,#4903));
#4874=EDGE_CURVE('',#4619,#4787,#4879,.T.);
#4879=LINE('',#4620,#4881);
#4881=VECTOR('',#4882,0.254);
#4882=DIRECTION('',(-1.0,0.0,0.0));
#4873=ORIENTED_EDGE('',*,*,#4874,.F.);
#4883=ORIENTED_EDGE('',*,*,#4618,.T.);
#4893=ORIENTED_EDGE('',*,*,#4707,.T.);
#4903=ORIENTED_EDGE('',*,*,#4796,.T.);
#4913=STYLED_ITEM('',(#34),#4914);
#4914=ADVANCED_FACE('',(#4920),#4915,.T.);
#4915=PLANE('',#4916);
#4916=AXIS2_PLACEMENT_3D('',#4917,#4918,#4919);
#4917=CARTESIAN_POINT('',(7.747,-5.74,0.0));
#4918=DIRECTION('',(0.0,-1.0,0.0));
#4919=DIRECTION('',(0.,0.,1.));
#4920=FACE_OUTER_BOUND('',#4921,.T.);
#4921=EDGE_LOOP('',(#4922,#4932,#4942,#4952,#4962,#4972));
#4922=ORIENTED_EDGE('',*,*,#4845,.T.);
#4932=ORIENTED_EDGE('',*,*,#4648,.T.);
#4945=CARTESIAN_POINT('',(7.239,-5.74,3.048));
#4944=VERTEX_POINT('',#4945);
#4943=EDGE_CURVE('',#4944,#4639,#4948,.T.);
#4948=LINE('',#4945,#4950);
#4950=VECTOR('',#4951,0.762);
#4951=DIRECTION('',(1.0,0.0,0.0));
#4942=ORIENTED_EDGE('',*,*,#4943,.F.);
#4955=CARTESIAN_POINT('',(7.239,-5.74,2.794));
#4954=VERTEX_POINT('',#4955);
#4953=EDGE_CURVE('',#4954,#4944,#4958,.T.);
#4958=LINE('',#4955,#4960);
#4960=VECTOR('',#4961,0.254);
#4961=DIRECTION('',(0.0,0.0,1.0));
#4952=ORIENTED_EDGE('',*,*,#4953,.F.);
#4963=EDGE_CURVE('',#4767,#4954,#4968,.T.);
#4968=LINE('',#4768,#4970);
#4970=VECTOR('',#4971,0.508);
#4971=DIRECTION('',(-1.0,0.0,0.0));
#4962=ORIENTED_EDGE('',*,*,#4963,.F.);
#4972=ORIENTED_EDGE('',*,*,#4766,.T.);
#4982=STYLED_ITEM('',(#34),#4983);
#4983=ADVANCED_FACE('',(#4989),#4984,.T.);
#4984=PLANE('',#4985);
#4985=AXIS2_PLACEMENT_3D('',#4986,#4987,#4988);
#4986=CARTESIAN_POINT('',(8.001,-4.42,0.0));
#4987=DIRECTION('',(0.0,1.0,0.0));
#4988=DIRECTION('',(0.,0.,1.));
#4989=FACE_OUTER_BOUND('',#4990,.T.);
#4990=EDGE_LOOP('',(#4991,#5001,#5011,#5021,#5031,#5041));
#4991=ORIENTED_EDGE('',*,*,#4874,.T.);
#5001=ORIENTED_EDGE('',*,*,#4786,.T.);
#5014=CARTESIAN_POINT('',(7.239,-4.42,2.794));
#5013=VERTEX_POINT('',#5014);
#5012=EDGE_CURVE('',#5013,#4777,#5017,.T.);
#5017=LINE('',#5014,#5019);
#5019=VECTOR('',#5020,0.508);
#5020=DIRECTION('',(1.0,0.0,0.0));
#5011=ORIENTED_EDGE('',*,*,#5012,.F.);
#5024=CARTESIAN_POINT('',(7.239,-4.42,3.048));
#5023=VERTEX_POINT('',#5024);
#5022=EDGE_CURVE('',#5023,#5013,#5027,.T.);
#5027=LINE('',#5024,#5029);
#5029=VECTOR('',#5030,0.254);
#5030=DIRECTION('',(0.0,0.0,-1.0));
#5021=ORIENTED_EDGE('',*,*,#5022,.F.);
#5032=EDGE_CURVE('',#4629,#5023,#5037,.T.);
#5037=LINE('',#4630,#5039);
#5039=VECTOR('',#5040,0.762);
#5040=DIRECTION('',(-1.0,0.0,0.0));
#5031=ORIENTED_EDGE('',*,*,#5032,.F.);
#5041=ORIENTED_EDGE('',*,*,#4628,.T.);
#5051=STYLED_ITEM('',(#34),#5052);
#5052=ADVANCED_FACE('',(#5058),#5053,.T.);
#5053=PLANE('',#5054);
#5054=AXIS2_PLACEMENT_3D('',#5055,#5056,#5057);
#5055=CARTESIAN_POINT('',(7.239,-4.42,2.794));
#5056=DIRECTION('',(0.0,0.0,-1.0));
#5057=DIRECTION('',(0.,1.,0.));
#5058=FACE_OUTER_BOUND('',#5059,.T.);
#5059=EDGE_LOOP('',(#5060,#5070,#5080,#5090));
#5060=ORIENTED_EDGE('',*,*,#5012,.T.);
#5070=ORIENTED_EDGE('',*,*,#4776,.T.);
#5080=ORIENTED_EDGE('',*,*,#4963,.T.);
#5091=EDGE_CURVE('',#5013,#4954,#5096,.T.);
#5096=LINE('',#5014,#5098);
#5098=VECTOR('',#5099,1.3208);
#5099=DIRECTION('',(0.0,-1.0,0.0));
#5090=ORIENTED_EDGE('',*,*,#5091,.F.);
#5100=STYLED_ITEM('',(#34),#5101);
#5101=ADVANCED_FACE('',(#5107),#5102,.T.);
#5102=PLANE('',#5103);
#5103=AXIS2_PLACEMENT_3D('',#5104,#5105,#5106);
#5104=CARTESIAN_POINT('',(7.239,-4.42,2.794));
#5105=DIRECTION('',(-1.0,0.0,0.0));
#5106=DIRECTION('',(0.,0.,1.));
#5107=FACE_OUTER_BOUND('',#5108,.T.);
#5108=EDGE_LOOP('',(#5109,#5119,#5129,#5139));
#5109=ORIENTED_EDGE('',*,*,#5091,.T.);
#5119=ORIENTED_EDGE('',*,*,#4953,.T.);
#5130=EDGE_CURVE('',#5023,#4944,#5135,.T.);
#5135=LINE('',#5024,#5137);
#5137=VECTOR('',#5138,1.3208);
#5138=DIRECTION('',(0.0,-1.0,0.0));
#5129=ORIENTED_EDGE('',*,*,#5130,.F.);
#5139=ORIENTED_EDGE('',*,*,#5022,.T.);
#5149=STYLED_ITEM('',(#34),#5150);
#5150=ADVANCED_FACE('',(#5156),#5151,.T.);
#5151=PLANE('',#5152);
#5152=AXIS2_PLACEMENT_3D('',#5153,#5154,#5155);
#5153=CARTESIAN_POINT('',(7.239,-5.74,3.048));
#5154=DIRECTION('',(0.0,0.0,1.0));
#5155=DIRECTION('',(0.,1.,0.));
#5156=FACE_OUTER_BOUND('',#5157,.T.);
#5157=EDGE_LOOP('',(#5158,#5168,#5178,#5188));
#5158=ORIENTED_EDGE('',*,*,#4943,.T.);
#5168=ORIENTED_EDGE('',*,*,#4638,.T.);
#5178=ORIENTED_EDGE('',*,*,#5032,.T.);
#5188=ORIENTED_EDGE('',*,*,#5130,.T.);
#5199=MANIFOLD_SOLID_BREP($,#5200);
#5200=CLOSED_SHELL('',(#5202,#5251,#5300,#5389,#5438,#5527,#5576,#5625,#5694,#5763,#5812,#5861));
#5201=STYLED_ITEM('',(#34),#5202);
#5202=ADVANCED_FACE('',(#5208),#5203,.T.);
#5203=PLANE('',#5204);
#5204=AXIS2_PLACEMENT_3D('',#5205,#5206,#5207);
#5205=CARTESIAN_POINT('',(7.747,-2.819,-2.54));
#5206=DIRECTION('',(0.0,0.0,-1.0));
#5207=DIRECTION('',(0.,1.,0.));
#5208=FACE_OUTER_BOUND('',#5209,.T.);
#5209=EDGE_LOOP('',(#5210,#5220,#5230,#5240));
#5213=CARTESIAN_POINT('',(7.747,-2.261,-2.54));
#5212=VERTEX_POINT('',#5213);
#5215=CARTESIAN_POINT('',(7.747,-2.819,-2.54));
#5214=VERTEX_POINT('',#5215);
#5211=EDGE_CURVE('',#5212,#5214,#5216,.T.);
#5216=LINE('',#5213,#5218);
#5218=VECTOR('',#5219,0.5588);
#5219=DIRECTION('',(0.0,-1.0,0.0));
#5210=ORIENTED_EDGE('',*,*,#5211,.F.);
#5223=CARTESIAN_POINT('',(8.001,-2.261,-2.54));
#5222=VERTEX_POINT('',#5223);
#5221=EDGE_CURVE('',#5222,#5212,#5226,.T.);
#5226=LINE('',#5223,#5228);
#5228=VECTOR('',#5229,0.254);
#5229=DIRECTION('',(-1.0,0.0,0.0));
#5220=ORIENTED_EDGE('',*,*,#5221,.F.);
#5233=CARTESIAN_POINT('',(8.001,-2.819,-2.54));
#5232=VERTEX_POINT('',#5233);
#5231=EDGE_CURVE('',#5232,#5222,#5236,.T.);
#5236=LINE('',#5233,#5238);
#5238=VECTOR('',#5239,0.5588);
#5239=DIRECTION('',(0.0,1.0,0.0));
#5230=ORIENTED_EDGE('',*,*,#5231,.F.);
#5241=EDGE_CURVE('',#5214,#5232,#5246,.T.);
#5246=LINE('',#5215,#5248);
#5248=VECTOR('',#5249,0.254);
#5249=DIRECTION('',(1.0,0.0,0.0));
#5240=ORIENTED_EDGE('',*,*,#5241,.F.);
#5250=STYLED_ITEM('',(#34),#5251);
#5251=ADVANCED_FACE('',(#5257),#5252,.T.);
#5252=PLANE('',#5253);
#5253=AXIS2_PLACEMENT_3D('',#5254,#5255,#5256);
#5254=CARTESIAN_POINT('',(7.747,-2.819,-2.54));
#5255=DIRECTION('',(0.0,-1.0,0.0));
#5256=DIRECTION('',(0.,0.,1.));
#5257=FACE_OUTER_BOUND('',#5258,.T.);
#5258=EDGE_LOOP('',(#5259,#5269,#5279,#5289));
#5259=ORIENTED_EDGE('',*,*,#5241,.T.);
#5272=CARTESIAN_POINT('',(8.001,-2.819,0.0));
#5271=VERTEX_POINT('',#5272);
#5270=EDGE_CURVE('',#5271,#5232,#5275,.T.);
#5275=LINE('',#5272,#5277);
#5277=VECTOR('',#5278,2.54);
#5278=DIRECTION('',(0.0,0.0,-1.0));
#5269=ORIENTED_EDGE('',*,*,#5270,.F.);
#5282=CARTESIAN_POINT('',(7.747,-2.819,0.0));
#5281=VERTEX_POINT('',#5282);
#5280=EDGE_CURVE('',#5281,#5271,#5285,.T.);
#5285=LINE('',#5282,#5287);
#5287=VECTOR('',#5288,0.254);
#5288=DIRECTION('',(1.0,0.0,0.0));
#5279=ORIENTED_EDGE('',*,*,#5280,.F.);
#5290=EDGE_CURVE('',#5214,#5281,#5295,.T.);
#5295=LINE('',#5215,#5297);
#5297=VECTOR('',#5298,2.54);
#5298=DIRECTION('',(0.0,0.0,1.0));
#5289=ORIENTED_EDGE('',*,*,#5290,.F.);
#5299=STYLED_ITEM('',(#34),#5300);
#5300=ADVANCED_FACE('',(#5306),#5301,.T.);
#5301=PLANE('',#5302);
#5302=AXIS2_PLACEMENT_3D('',#5303,#5304,#5305);
#5303=CARTESIAN_POINT('',(8.001,-2.819,-2.54));
#5304=DIRECTION('',(1.0,0.0,0.0));
#5305=DIRECTION('',(0.,0.,1.));
#5306=FACE_OUTER_BOUND('',#5307,.T.);
#5307=EDGE_LOOP('',(#5308,#5318,#5328,#5338,#5348,#5358,#5368,#5378));
#5308=ORIENTED_EDGE('',*,*,#5231,.T.);
#5321=CARTESIAN_POINT('',(8.001,-2.261,0.0));
#5320=VERTEX_POINT('',#5321);
#5319=EDGE_CURVE('',#5320,#5222,#5324,.T.);
#5324=LINE('',#5321,#5326);
#5326=VECTOR('',#5327,2.54);
#5327=DIRECTION('',(0.0,0.0,-1.0));
#5318=ORIENTED_EDGE('',*,*,#5319,.F.);
#5331=CARTESIAN_POINT('',(8.001,-1.88,0.0));
#5330=VERTEX_POINT('',#5331);
#5329=EDGE_CURVE('',#5330,#5320,#5334,.T.);
#5334=LINE('',#5331,#5336);
#5336=VECTOR('',#5337,0.381);
#5337=DIRECTION('',(0.0,-1.0,0.0));
#5328=ORIENTED_EDGE('',*,*,#5329,.F.);
#5341=CARTESIAN_POINT('',(8.001,-1.88,3.048));
#5340=VERTEX_POINT('',#5341);
#5339=EDGE_CURVE('',#5340,#5330,#5344,.T.);
#5344=LINE('',#5341,#5346);
#5346=VECTOR('',#5347,3.048);
#5347=DIRECTION('',(0.0,0.0,-1.0));
#5338=ORIENTED_EDGE('',*,*,#5339,.F.);
#5351=CARTESIAN_POINT('',(8.001,-3.2,3.048));
#5350=VERTEX_POINT('',#5351);
#5349=EDGE_CURVE('',#5350,#5340,#5354,.T.);
#5354=LINE('',#5351,#5356);
#5356=VECTOR('',#5357,1.3208);
#5357=DIRECTION('',(0.0,1.0,0.0));
#5348=ORIENTED_EDGE('',*,*,#5349,.F.);
#5361=CARTESIAN_POINT('',(8.001,-3.2,0.0));
#5360=VERTEX_POINT('',#5361);
#5359=EDGE_CURVE('',#5360,#5350,#5364,.T.);
#5364=LINE('',#5361,#5366);
#5366=VECTOR('',#5367,3.048);
#5367=DIRECTION('',(0.0,0.0,1.0));
#5358=ORIENTED_EDGE('',*,*,#5359,.F.);
#5369=EDGE_CURVE('',#5271,#5360,#5374,.T.);
#5374=LINE('',#5272,#5376);
#5376=VECTOR('',#5377,0.381);
#5377=DIRECTION('',(0.0,-1.0,0.0));
#5368=ORIENTED_EDGE('',*,*,#5369,.F.);
#5378=ORIENTED_EDGE('',*,*,#5270,.T.);
#5388=STYLED_ITEM('',(#34),#5389);
#5389=ADVANCED_FACE('',(#5395),#5390,.T.);
#5390=PLANE('',#5391);
#5391=AXIS2_PLACEMENT_3D('',#5392,#5393,#5394);
#5392=CARTESIAN_POINT('',(8.001,-2.261,-2.54));
#5393=DIRECTION('',(0.0,1.0,0.0));
#5394=DIRECTION('',(0.,0.,1.));
#5395=FACE_OUTER_BOUND('',#5396,.T.);
#5396=EDGE_LOOP('',(#5397,#5407,#5417,#5427));
#5397=ORIENTED_EDGE('',*,*,#5221,.T.);
#5410=CARTESIAN_POINT('',(7.747,-2.261,0.0));
#5409=VERTEX_POINT('',#5410);
#5408=EDGE_CURVE('',#5409,#5212,#5413,.T.);
#5413=LINE('',#5410,#5415);
#5415=VECTOR('',#5416,2.54);
#5416=DIRECTION('',(0.0,0.0,-1.0));
#5407=ORIENTED_EDGE('',*,*,#5408,.F.);
#5418=EDGE_CURVE('',#5320,#5409,#5423,.T.);
#5423=LINE('',#5321,#5425);
#5425=VECTOR('',#5426,0.254);
#5426=DIRECTION('',(-1.0,0.0,0.0));
#5417=ORIENTED_EDGE('',*,*,#5418,.F.);
#5427=ORIENTED_EDGE('',*,*,#5319,.T.);
#5437=STYLED_ITEM('',(#34),#5438);
#5438=ADVANCED_FACE('',(#5444),#5439,.T.);
#5439=PLANE('',#5440);
#5440=AXIS2_PLACEMENT_3D('',#5441,#5442,#5443);
#5441=CARTESIAN_POINT('',(7.747,-2.261,-2.54));
#5442=DIRECTION('',(-1.0,0.0,0.0));
#5443=DIRECTION('',(0.,0.,1.));
#5444=FACE_OUTER_BOUND('',#5445,.T.);
#5445=EDGE_LOOP('',(#5446,#5456,#5466,#5476,#5486,#5496,#5506,#5516));
#5446=ORIENTED_EDGE('',*,*,#5211,.T.);
#5456=ORIENTED_EDGE('',*,*,#5290,.T.);
#5469=CARTESIAN_POINT('',(7.747,-3.2,0.0));
#5468=VERTEX_POINT('',#5469);
#5467=EDGE_CURVE('',#5468,#5281,#5472,.T.);
#5472=LINE('',#5469,#5474);
#5474=VECTOR('',#5475,0.381);
#5475=DIRECTION('',(0.0,1.0,0.0));
#5466=ORIENTED_EDGE('',*,*,#5467,.F.);
#5479=CARTESIAN_POINT('',(7.747,-3.2,2.794));
#5478=VERTEX_POINT('',#5479);
#5477=EDGE_CURVE('',#5478,#5468,#5482,.T.);
#5482=LINE('',#5479,#5484);
#5484=VECTOR('',#5485,2.794);
#5485=DIRECTION('',(0.0,0.0,-1.0));
#5476=ORIENTED_EDGE('',*,*,#5477,.F.);
#5489=CARTESIAN_POINT('',(7.747,-1.88,2.794));
#5488=VERTEX_POINT('',#5489);
#5487=EDGE_CURVE('',#5488,#5478,#5492,.T.);
#5492=LINE('',#5489,#5494);
#5494=VECTOR('',#5495,1.3208);
#5495=DIRECTION('',(0.0,-1.0,0.0));
#5486=ORIENTED_EDGE('',*,*,#5487,.F.);
#5499=CARTESIAN_POINT('',(7.747,-1.88,0.0));
#5498=VERTEX_POINT('',#5499);
#5497=EDGE_CURVE('',#5498,#5488,#5502,.T.);
#5502=LINE('',#5499,#5504);
#5504=VECTOR('',#5505,2.794);
#5505=DIRECTION('',(0.0,0.0,1.0));
#5496=ORIENTED_EDGE('',*,*,#5497,.F.);
#5507=EDGE_CURVE('',#5409,#5498,#5512,.T.);
#5512=LINE('',#5410,#5514);
#5514=VECTOR('',#5515,0.381);
#5515=DIRECTION('',(0.0,1.0,0.0));
#5506=ORIENTED_EDGE('',*,*,#5507,.F.);
#5516=ORIENTED_EDGE('',*,*,#5408,.T.);
#5526=STYLED_ITEM('',(#34),#5527);
#5527=ADVANCED_FACE('',(#5533),#5528,.T.);
#5528=PLANE('',#5529);
#5529=AXIS2_PLACEMENT_3D('',#5530,#5531,#5532);
#5530=CARTESIAN_POINT('',(7.747,-2.819,0.0));
#5531=DIRECTION('',(0.0,0.0,-1.0));
#5532=DIRECTION('',(0.,1.,0.));
#5533=FACE_OUTER_BOUND('',#5534,.T.);
#5534=EDGE_LOOP('',(#5535,#5545,#5555,#5565));
#5535=ORIENTED_EDGE('',*,*,#5280,.T.);
#5545=ORIENTED_EDGE('',*,*,#5369,.T.);
#5556=EDGE_CURVE('',#5468,#5360,#5561,.T.);
#5561=LINE('',#5469,#5563);
#5563=VECTOR('',#5564,0.254);
#5564=DIRECTION('',(1.0,0.0,0.0));
#5555=ORIENTED_EDGE('',*,*,#5556,.F.);
#5565=ORIENTED_EDGE('',*,*,#5467,.T.);
#5575=STYLED_ITEM('',(#34),#5576);
#5576=ADVANCED_FACE('',(#5582),#5577,.T.);
#5577=PLANE('',#5578);
#5578=AXIS2_PLACEMENT_3D('',#5579,#5580,#5581);
#5579=CARTESIAN_POINT('',(7.747,-1.88,0.0));
#5580=DIRECTION('',(0.0,0.0,-1.0));
#5581=DIRECTION('',(0.,1.,0.));
#5582=FACE_OUTER_BOUND('',#5583,.T.);
#5583=EDGE_LOOP('',(#5584,#5594,#5604,#5614));
#5585=EDGE_CURVE('',#5330,#5498,#5590,.T.);
#5590=LINE('',#5331,#5592);
#5592=VECTOR('',#5593,0.254);
#5593=DIRECTION('',(-1.0,0.0,0.0));
#5584=ORIENTED_EDGE('',*,*,#5585,.F.);
#5594=ORIENTED_EDGE('',*,*,#5329,.T.);
#5604=ORIENTED_EDGE('',*,*,#5418,.T.);
#5614=ORIENTED_EDGE('',*,*,#5507,.T.);
#5624=STYLED_ITEM('',(#34),#5625);
#5625=ADVANCED_FACE('',(#5631),#5626,.T.);
#5626=PLANE('',#5627);
#5627=AXIS2_PLACEMENT_3D('',#5628,#5629,#5630);
#5628=CARTESIAN_POINT('',(7.747,-3.2,0.0));
#5629=DIRECTION('',(0.0,-1.0,0.0));
#5630=DIRECTION('',(0.,0.,1.));
#5631=FACE_OUTER_BOUND('',#5632,.T.);
#5632=EDGE_LOOP('',(#5633,#5643,#5653,#5663,#5673,#5683));
#5633=ORIENTED_EDGE('',*,*,#5556,.T.);
#5643=ORIENTED_EDGE('',*,*,#5359,.T.);
#5656=CARTESIAN_POINT('',(7.239,-3.2,3.048));
#5655=VERTEX_POINT('',#5656);
#5654=EDGE_CURVE('',#5655,#5350,#5659,.T.);
#5659=LINE('',#5656,#5661);
#5661=VECTOR('',#5662,0.762);
#5662=DIRECTION('',(1.0,0.0,0.0));
#5653=ORIENTED_EDGE('',*,*,#5654,.F.);
#5666=CARTESIAN_POINT('',(7.239,-3.2,2.794));
#5665=VERTEX_POINT('',#5666);
#5664=EDGE_CURVE('',#5665,#5655,#5669,.T.);
#5669=LINE('',#5666,#5671);
#5671=VECTOR('',#5672,0.254);
#5672=DIRECTION('',(0.0,0.0,1.0));
#5663=ORIENTED_EDGE('',*,*,#5664,.F.);
#5674=EDGE_CURVE('',#5478,#5665,#5679,.T.);
#5679=LINE('',#5479,#5681);
#5681=VECTOR('',#5682,0.508);
#5682=DIRECTION('',(-1.0,0.0,0.0));
#5673=ORIENTED_EDGE('',*,*,#5674,.F.);
#5683=ORIENTED_EDGE('',*,*,#5477,.T.);
#5693=STYLED_ITEM('',(#34),#5694);
#5694=ADVANCED_FACE('',(#5700),#5695,.T.);
#5695=PLANE('',#5696);
#5696=AXIS2_PLACEMENT_3D('',#5697,#5698,#5699);
#5697=CARTESIAN_POINT('',(8.001,-1.88,0.0));
#5698=DIRECTION('',(0.0,1.0,0.0));
#5699=DIRECTION('',(0.,0.,1.));
#5700=FACE_OUTER_BOUND('',#5701,.T.);
#5701=EDGE_LOOP('',(#5702,#5712,#5722,#5732,#5742,#5752));
#5702=ORIENTED_EDGE('',*,*,#5585,.T.);
#5712=ORIENTED_EDGE('',*,*,#5497,.T.);
#5725=CARTESIAN_POINT('',(7.239,-1.88,2.794));
#5724=VERTEX_POINT('',#5725);
#5723=EDGE_CURVE('',#5724,#5488,#5728,.T.);
#5728=LINE('',#5725,#5730);
#5730=VECTOR('',#5731,0.508);
#5731=DIRECTION('',(1.0,0.0,0.0));
#5722=ORIENTED_EDGE('',*,*,#5723,.F.);
#5735=CARTESIAN_POINT('',(7.239,-1.88,3.048));
#5734=VERTEX_POINT('',#5735);
#5733=EDGE_CURVE('',#5734,#5724,#5738,.T.);
#5738=LINE('',#5735,#5740);
#5740=VECTOR('',#5741,0.254);
#5741=DIRECTION('',(0.0,0.0,-1.0));
#5732=ORIENTED_EDGE('',*,*,#5733,.F.);
#5743=EDGE_CURVE('',#5340,#5734,#5748,.T.);
#5748=LINE('',#5341,#5750);
#5750=VECTOR('',#5751,0.762);
#5751=DIRECTION('',(-1.0,0.0,0.0));
#5742=ORIENTED_EDGE('',*,*,#5743,.F.);
#5752=ORIENTED_EDGE('',*,*,#5339,.T.);
#5762=STYLED_ITEM('',(#34),#5763);
#5763=ADVANCED_FACE('',(#5769),#5764,.T.);
#5764=PLANE('',#5765);
#5765=AXIS2_PLACEMENT_3D('',#5766,#5767,#5768);
#5766=CARTESIAN_POINT('',(7.239,-1.88,2.794));
#5767=DIRECTION('',(0.0,0.0,-1.0));
#5768=DIRECTION('',(0.,1.,0.));
#5769=FACE_OUTER_BOUND('',#5770,.T.);
#5770=EDGE_LOOP('',(#5771,#5781,#5791,#5801));
#5771=ORIENTED_EDGE('',*,*,#5723,.T.);
#5781=ORIENTED_EDGE('',*,*,#5487,.T.);
#5791=ORIENTED_EDGE('',*,*,#5674,.T.);
#5802=EDGE_CURVE('',#5724,#5665,#5807,.T.);
#5807=LINE('',#5725,#5809);
#5809=VECTOR('',#5810,1.3208);
#5810=DIRECTION('',(0.0,-1.0,0.0));
#5801=ORIENTED_EDGE('',*,*,#5802,.F.);
#5811=STYLED_ITEM('',(#34),#5812);
#5812=ADVANCED_FACE('',(#5818),#5813,.T.);
#5813=PLANE('',#5814);
#5814=AXIS2_PLACEMENT_3D('',#5815,#5816,#5817);
#5815=CARTESIAN_POINT('',(7.239,-1.88,2.794));
#5816=DIRECTION('',(-1.0,0.0,0.0));
#5817=DIRECTION('',(0.,0.,1.));
#5818=FACE_OUTER_BOUND('',#5819,.T.);
#5819=EDGE_LOOP('',(#5820,#5830,#5840,#5850));
#5820=ORIENTED_EDGE('',*,*,#5802,.T.);
#5830=ORIENTED_EDGE('',*,*,#5664,.T.);
#5841=EDGE_CURVE('',#5734,#5655,#5846,.T.);
#5846=LINE('',#5735,#5848);
#5848=VECTOR('',#5849,1.3208);
#5849=DIRECTION('',(0.0,-1.0,0.0));
#5840=ORIENTED_EDGE('',*,*,#5841,.F.);
#5850=ORIENTED_EDGE('',*,*,#5733,.T.);
#5860=STYLED_ITEM('',(#34),#5861);
#5861=ADVANCED_FACE('',(#5867),#5862,.T.);
#5862=PLANE('',#5863);
#5863=AXIS2_PLACEMENT_3D('',#5864,#5865,#5866);
#5864=CARTESIAN_POINT('',(7.239,-3.2,3.048));
#5865=DIRECTION('',(0.0,0.0,1.0));
#5866=DIRECTION('',(0.,1.,0.));
#5867=FACE_OUTER_BOUND('',#5868,.T.);
#5868=EDGE_LOOP('',(#5869,#5879,#5889,#5899));
#5869=ORIENTED_EDGE('',*,*,#5654,.T.);
#5879=ORIENTED_EDGE('',*,*,#5349,.T.);
#5889=ORIENTED_EDGE('',*,*,#5743,.T.);
#5899=ORIENTED_EDGE('',*,*,#5841,.T.);
#5910=MANIFOLD_SOLID_BREP($,#5911);
#5911=CLOSED_SHELL('',(#5913,#5962,#6011,#6100,#6149,#6238,#6287,#6336,#6405,#6474,#6523,#6572));
#5912=STYLED_ITEM('',(#34),#5913);
#5913=ADVANCED_FACE('',(#5919),#5914,.T.);
#5914=PLANE('',#5915);
#5915=AXIS2_PLACEMENT_3D('',#5916,#5917,#5918);
#5916=CARTESIAN_POINT('',(7.747,-0.279,-2.54));
#5917=DIRECTION('',(0.0,0.0,-1.0));
#5918=DIRECTION('',(0.,1.,0.));
#5919=FACE_OUTER_BOUND('',#5920,.T.);
#5920=EDGE_LOOP('',(#5921,#5931,#5941,#5951));
#5924=CARTESIAN_POINT('',(7.747,0.279,-2.54));
#5923=VERTEX_POINT('',#5924);
#5926=CARTESIAN_POINT('',(7.747,-0.279,-2.54));
#5925=VERTEX_POINT('',#5926);
#5922=EDGE_CURVE('',#5923,#5925,#5927,.T.);
#5927=LINE('',#5924,#5929);
#5929=VECTOR('',#5930,0.5588);
#5930=DIRECTION('',(0.0,-1.0,0.0));
#5921=ORIENTED_EDGE('',*,*,#5922,.F.);
#5934=CARTESIAN_POINT('',(8.001,0.279,-2.54));
#5933=VERTEX_POINT('',#5934);
#5932=EDGE_CURVE('',#5933,#5923,#5937,.T.);
#5937=LINE('',#5934,#5939);
#5939=VECTOR('',#5940,0.254);
#5940=DIRECTION('',(-1.0,0.0,0.0));
#5931=ORIENTED_EDGE('',*,*,#5932,.F.);
#5944=CARTESIAN_POINT('',(8.001,-0.279,-2.54));
#5943=VERTEX_POINT('',#5944);
#5942=EDGE_CURVE('',#5943,#5933,#5947,.T.);
#5947=LINE('',#5944,#5949);
#5949=VECTOR('',#5950,0.5588);
#5950=DIRECTION('',(0.0,1.0,0.0));
#5941=ORIENTED_EDGE('',*,*,#5942,.F.);
#5952=EDGE_CURVE('',#5925,#5943,#5957,.T.);
#5957=LINE('',#5926,#5959);
#5959=VECTOR('',#5960,0.254);
#5960=DIRECTION('',(1.0,0.0,0.0));
#5951=ORIENTED_EDGE('',*,*,#5952,.F.);
#5961=STYLED_ITEM('',(#34),#5962);
#5962=ADVANCED_FACE('',(#5968),#5963,.T.);
#5963=PLANE('',#5964);
#5964=AXIS2_PLACEMENT_3D('',#5965,#5966,#5967);
#5965=CARTESIAN_POINT('',(7.747,-0.279,-2.54));
#5966=DIRECTION('',(0.0,-1.0,0.0));
#5967=DIRECTION('',(0.,0.,1.));
#5968=FACE_OUTER_BOUND('',#5969,.T.);
#5969=EDGE_LOOP('',(#5970,#5980,#5990,#6000));
#5970=ORIENTED_EDGE('',*,*,#5952,.T.);
#5983=CARTESIAN_POINT('',(8.001,-0.279,0.0));
#5982=VERTEX_POINT('',#5983);
#5981=EDGE_CURVE('',#5982,#5943,#5986,.T.);
#5986=LINE('',#5983,#5988);
#5988=VECTOR('',#5989,2.54);
#5989=DIRECTION('',(0.0,0.0,-1.0));
#5980=ORIENTED_EDGE('',*,*,#5981,.F.);
#5993=CARTESIAN_POINT('',(7.747,-0.279,0.0));
#5992=VERTEX_POINT('',#5993);
#5991=EDGE_CURVE('',#5992,#5982,#5996,.T.);
#5996=LINE('',#5993,#5998);
#5998=VECTOR('',#5999,0.254);
#5999=DIRECTION('',(1.0,0.0,0.0));
#5990=ORIENTED_EDGE('',*,*,#5991,.F.);
#6001=EDGE_CURVE('',#5925,#5992,#6006,.T.);
#6006=LINE('',#5926,#6008);
#6008=VECTOR('',#6009,2.54);
#6009=DIRECTION('',(0.0,0.0,1.0));
#6000=ORIENTED_EDGE('',*,*,#6001,.F.);
#6010=STYLED_ITEM('',(#34),#6011);
#6011=ADVANCED_FACE('',(#6017),#6012,.T.);
#6012=PLANE('',#6013);
#6013=AXIS2_PLACEMENT_3D('',#6014,#6015,#6016);
#6014=CARTESIAN_POINT('',(8.001,-0.279,-2.54));
#6015=DIRECTION('',(1.0,0.0,0.0));
#6016=DIRECTION('',(0.,0.,1.));
#6017=FACE_OUTER_BOUND('',#6018,.T.);
#6018=EDGE_LOOP('',(#6019,#6029,#6039,#6049,#6059,#6069,#6079,#6089));
#6019=ORIENTED_EDGE('',*,*,#5942,.T.);
#6032=CARTESIAN_POINT('',(8.001,0.279,0.0));
#6031=VERTEX_POINT('',#6032);
#6030=EDGE_CURVE('',#6031,#5933,#6035,.T.);
#6035=LINE('',#6032,#6037);
#6037=VECTOR('',#6038,2.54);
#6038=DIRECTION('',(0.0,0.0,-1.0));
#6029=ORIENTED_EDGE('',*,*,#6030,.F.);
#6042=CARTESIAN_POINT('',(8.001,0.66,0.0));
#6041=VERTEX_POINT('',#6042);
#6040=EDGE_CURVE('',#6041,#6031,#6045,.T.);
#6045=LINE('',#6042,#6047);
#6047=VECTOR('',#6048,0.381);
#6048=DIRECTION('',(0.0,-1.0,0.0));
#6039=ORIENTED_EDGE('',*,*,#6040,.F.);
#6052=CARTESIAN_POINT('',(8.001,0.66,3.048));
#6051=VERTEX_POINT('',#6052);
#6050=EDGE_CURVE('',#6051,#6041,#6055,.T.);
#6055=LINE('',#6052,#6057);
#6057=VECTOR('',#6058,3.048);
#6058=DIRECTION('',(0.0,0.0,-1.0));
#6049=ORIENTED_EDGE('',*,*,#6050,.F.);
#6062=CARTESIAN_POINT('',(8.001,-0.66,3.048));
#6061=VERTEX_POINT('',#6062);
#6060=EDGE_CURVE('',#6061,#6051,#6065,.T.);
#6065=LINE('',#6062,#6067);
#6067=VECTOR('',#6068,1.3208);
#6068=DIRECTION('',(0.0,1.0,0.0));
#6059=ORIENTED_EDGE('',*,*,#6060,.F.);
#6072=CARTESIAN_POINT('',(8.001,-0.66,0.0));
#6071=VERTEX_POINT('',#6072);
#6070=EDGE_CURVE('',#6071,#6061,#6075,.T.);
#6075=LINE('',#6072,#6077);
#6077=VECTOR('',#6078,3.048);
#6078=DIRECTION('',(0.0,0.0,1.0));
#6069=ORIENTED_EDGE('',*,*,#6070,.F.);
#6080=EDGE_CURVE('',#5982,#6071,#6085,.T.);
#6085=LINE('',#5983,#6087);
#6087=VECTOR('',#6088,0.381);
#6088=DIRECTION('',(0.0,-1.0,0.0));
#6079=ORIENTED_EDGE('',*,*,#6080,.F.);
#6089=ORIENTED_EDGE('',*,*,#5981,.T.);
#6099=STYLED_ITEM('',(#34),#6100);
#6100=ADVANCED_FACE('',(#6106),#6101,.T.);
#6101=PLANE('',#6102);
#6102=AXIS2_PLACEMENT_3D('',#6103,#6104,#6105);
#6103=CARTESIAN_POINT('',(8.001,0.279,-2.54));
#6104=DIRECTION('',(0.0,1.0,0.0));
#6105=DIRECTION('',(0.,0.,1.));
#6106=FACE_OUTER_BOUND('',#6107,.T.);
#6107=EDGE_LOOP('',(#6108,#6118,#6128,#6138));
#6108=ORIENTED_EDGE('',*,*,#5932,.T.);
#6121=CARTESIAN_POINT('',(7.747,0.279,0.0));
#6120=VERTEX_POINT('',#6121);
#6119=EDGE_CURVE('',#6120,#5923,#6124,.T.);
#6124=LINE('',#6121,#6126);
#6126=VECTOR('',#6127,2.54);
#6127=DIRECTION('',(0.0,0.0,-1.0));
#6118=ORIENTED_EDGE('',*,*,#6119,.F.);
#6129=EDGE_CURVE('',#6031,#6120,#6134,.T.);
#6134=LINE('',#6032,#6136);
#6136=VECTOR('',#6137,0.254);
#6137=DIRECTION('',(-1.0,0.0,0.0));
#6128=ORIENTED_EDGE('',*,*,#6129,.F.);
#6138=ORIENTED_EDGE('',*,*,#6030,.T.);
#6148=STYLED_ITEM('',(#34),#6149);
#6149=ADVANCED_FACE('',(#6155),#6150,.T.);
#6150=PLANE('',#6151);
#6151=AXIS2_PLACEMENT_3D('',#6152,#6153,#6154);
#6152=CARTESIAN_POINT('',(7.747,0.279,-2.54));
#6153=DIRECTION('',(-1.0,0.0,0.0));
#6154=DIRECTION('',(0.,0.,1.));
#6155=FACE_OUTER_BOUND('',#6156,.T.);
#6156=EDGE_LOOP('',(#6157,#6167,#6177,#6187,#6197,#6207,#6217,#6227));
#6157=ORIENTED_EDGE('',*,*,#5922,.T.);
#6167=ORIENTED_EDGE('',*,*,#6001,.T.);
#6180=CARTESIAN_POINT('',(7.747,-0.66,0.0));
#6179=VERTEX_POINT('',#6180);
#6178=EDGE_CURVE('',#6179,#5992,#6183,.T.);
#6183=LINE('',#6180,#6185);
#6185=VECTOR('',#6186,0.381);
#6186=DIRECTION('',(0.0,1.0,0.0));
#6177=ORIENTED_EDGE('',*,*,#6178,.F.);
#6190=CARTESIAN_POINT('',(7.747,-0.66,2.794));
#6189=VERTEX_POINT('',#6190);
#6188=EDGE_CURVE('',#6189,#6179,#6193,.T.);
#6193=LINE('',#6190,#6195);
#6195=VECTOR('',#6196,2.794);
#6196=DIRECTION('',(0.0,0.0,-1.0));
#6187=ORIENTED_EDGE('',*,*,#6188,.F.);
#6200=CARTESIAN_POINT('',(7.747,0.66,2.794));
#6199=VERTEX_POINT('',#6200);
#6198=EDGE_CURVE('',#6199,#6189,#6203,.T.);
#6203=LINE('',#6200,#6205);
#6205=VECTOR('',#6206,1.3208);
#6206=DIRECTION('',(0.0,-1.0,0.0));
#6197=ORIENTED_EDGE('',*,*,#6198,.F.);
#6210=CARTESIAN_POINT('',(7.747,0.66,0.0));
#6209=VERTEX_POINT('',#6210);
#6208=EDGE_CURVE('',#6209,#6199,#6213,.T.);
#6213=LINE('',#6210,#6215);
#6215=VECTOR('',#6216,2.794);
#6216=DIRECTION('',(0.0,0.0,1.0));
#6207=ORIENTED_EDGE('',*,*,#6208,.F.);
#6218=EDGE_CURVE('',#6120,#6209,#6223,.T.);
#6223=LINE('',#6121,#6225);
#6225=VECTOR('',#6226,0.381);
#6226=DIRECTION('',(0.0,1.0,0.0));
#6217=ORIENTED_EDGE('',*,*,#6218,.F.);
#6227=ORIENTED_EDGE('',*,*,#6119,.T.);
#6237=STYLED_ITEM('',(#34),#6238);
#6238=ADVANCED_FACE('',(#6244),#6239,.T.);
#6239=PLANE('',#6240);
#6240=AXIS2_PLACEMENT_3D('',#6241,#6242,#6243);
#6241=CARTESIAN_POINT('',(7.747,-0.279,0.0));
#6242=DIRECTION('',(0.0,0.0,-1.0));
#6243=DIRECTION('',(0.,1.,0.));
#6244=FACE_OUTER_BOUND('',#6245,.T.);
#6245=EDGE_LOOP('',(#6246,#6256,#6266,#6276));
#6246=ORIENTED_EDGE('',*,*,#5991,.T.);
#6256=ORIENTED_EDGE('',*,*,#6080,.T.);
#6267=EDGE_CURVE('',#6179,#6071,#6272,.T.);
#6272=LINE('',#6180,#6274);
#6274=VECTOR('',#6275,0.254);
#6275=DIRECTION('',(1.0,0.0,0.0));
#6266=ORIENTED_EDGE('',*,*,#6267,.F.);
#6276=ORIENTED_EDGE('',*,*,#6178,.T.);
#6286=STYLED_ITEM('',(#34),#6287);
#6287=ADVANCED_FACE('',(#6293),#6288,.T.);
#6288=PLANE('',#6289);
#6289=AXIS2_PLACEMENT_3D('',#6290,#6291,#6292);
#6290=CARTESIAN_POINT('',(7.747,0.66,0.0));
#6291=DIRECTION('',(0.0,0.0,-1.0));
#6292=DIRECTION('',(0.,1.,0.));
#6293=FACE_OUTER_BOUND('',#6294,.T.);
#6294=EDGE_LOOP('',(#6295,#6305,#6315,#6325));
#6296=EDGE_CURVE('',#6041,#6209,#6301,.T.);
#6301=LINE('',#6042,#6303);
#6303=VECTOR('',#6304,0.254);
#6304=DIRECTION('',(-1.0,0.0,0.0));
#6295=ORIENTED_EDGE('',*,*,#6296,.F.);
#6305=ORIENTED_EDGE('',*,*,#6040,.T.);
#6315=ORIENTED_EDGE('',*,*,#6129,.T.);
#6325=ORIENTED_EDGE('',*,*,#6218,.T.);
#6335=STYLED_ITEM('',(#34),#6336);
#6336=ADVANCED_FACE('',(#6342),#6337,.T.);
#6337=PLANE('',#6338);
#6338=AXIS2_PLACEMENT_3D('',#6339,#6340,#6341);
#6339=CARTESIAN_POINT('',(7.747,-0.66,0.0));
#6340=DIRECTION('',(0.0,-1.0,0.0));
#6341=DIRECTION('',(0.,0.,1.));
#6342=FACE_OUTER_BOUND('',#6343,.T.);
#6343=EDGE_LOOP('',(#6344,#6354,#6364,#6374,#6384,#6394));
#6344=ORIENTED_EDGE('',*,*,#6267,.T.);
#6354=ORIENTED_EDGE('',*,*,#6070,.T.);
#6367=CARTESIAN_POINT('',(7.239,-0.66,3.048));
#6366=VERTEX_POINT('',#6367);
#6365=EDGE_CURVE('',#6366,#6061,#6370,.T.);
#6370=LINE('',#6367,#6372);
#6372=VECTOR('',#6373,0.762);
#6373=DIRECTION('',(1.0,0.0,0.0));
#6364=ORIENTED_EDGE('',*,*,#6365,.F.);
#6377=CARTESIAN_POINT('',(7.239,-0.66,2.794));
#6376=VERTEX_POINT('',#6377);
#6375=EDGE_CURVE('',#6376,#6366,#6380,.T.);
#6380=LINE('',#6377,#6382);
#6382=VECTOR('',#6383,0.254);
#6383=DIRECTION('',(0.0,0.0,1.0));
#6374=ORIENTED_EDGE('',*,*,#6375,.F.);
#6385=EDGE_CURVE('',#6189,#6376,#6390,.T.);
#6390=LINE('',#6190,#6392);
#6392=VECTOR('',#6393,0.508);
#6393=DIRECTION('',(-1.0,0.0,0.0));
#6384=ORIENTED_EDGE('',*,*,#6385,.F.);
#6394=ORIENTED_EDGE('',*,*,#6188,.T.);
#6404=STYLED_ITEM('',(#34),#6405);
#6405=ADVANCED_FACE('',(#6411),#6406,.T.);
#6406=PLANE('',#6407);
#6407=AXIS2_PLACEMENT_3D('',#6408,#6409,#6410);
#6408=CARTESIAN_POINT('',(8.001,0.66,0.0));
#6409=DIRECTION('',(0.0,1.0,0.0));
#6410=DIRECTION('',(0.,0.,1.));
#6411=FACE_OUTER_BOUND('',#6412,.T.);
#6412=EDGE_LOOP('',(#6413,#6423,#6433,#6443,#6453,#6463));
#6413=ORIENTED_EDGE('',*,*,#6296,.T.);
#6423=ORIENTED_EDGE('',*,*,#6208,.T.);
#6436=CARTESIAN_POINT('',(7.239,0.66,2.794));
#6435=VERTEX_POINT('',#6436);
#6434=EDGE_CURVE('',#6435,#6199,#6439,.T.);
#6439=LINE('',#6436,#6441);
#6441=VECTOR('',#6442,0.508);
#6442=DIRECTION('',(1.0,0.0,0.0));
#6433=ORIENTED_EDGE('',*,*,#6434,.F.);
#6446=CARTESIAN_POINT('',(7.239,0.66,3.048));
#6445=VERTEX_POINT('',#6446);
#6444=EDGE_CURVE('',#6445,#6435,#6449,.T.);
#6449=LINE('',#6446,#6451);
#6451=VECTOR('',#6452,0.254);
#6452=DIRECTION('',(0.0,0.0,-1.0));
#6443=ORIENTED_EDGE('',*,*,#6444,.F.);
#6454=EDGE_CURVE('',#6051,#6445,#6459,.T.);
#6459=LINE('',#6052,#6461);
#6461=VECTOR('',#6462,0.762);
#6462=DIRECTION('',(-1.0,0.0,0.0));
#6453=ORIENTED_EDGE('',*,*,#6454,.F.);
#6463=ORIENTED_EDGE('',*,*,#6050,.T.);
#6473=STYLED_ITEM('',(#34),#6474);
#6474=ADVANCED_FACE('',(#6480),#6475,.T.);
#6475=PLANE('',#6476);
#6476=AXIS2_PLACEMENT_3D('',#6477,#6478,#6479);
#6477=CARTESIAN_POINT('',(7.239,0.66,2.794));
#6478=DIRECTION('',(0.0,0.0,-1.0));
#6479=DIRECTION('',(0.,1.,0.));
#6480=FACE_OUTER_BOUND('',#6481,.T.);
#6481=EDGE_LOOP('',(#6482,#6492,#6502,#6512));
#6482=ORIENTED_EDGE('',*,*,#6434,.T.);
#6492=ORIENTED_EDGE('',*,*,#6198,.T.);
#6502=ORIENTED_EDGE('',*,*,#6385,.T.);
#6513=EDGE_CURVE('',#6435,#6376,#6518,.T.);
#6518=LINE('',#6436,#6520);
#6520=VECTOR('',#6521,1.3208);
#6521=DIRECTION('',(0.0,-1.0,0.0));
#6512=ORIENTED_EDGE('',*,*,#6513,.F.);
#6522=STYLED_ITEM('',(#34),#6523);
#6523=ADVANCED_FACE('',(#6529),#6524,.T.);
#6524=PLANE('',#6525);
#6525=AXIS2_PLACEMENT_3D('',#6526,#6527,#6528);
#6526=CARTESIAN_POINT('',(7.239,0.66,2.794));
#6527=DIRECTION('',(-1.0,0.0,0.0));
#6528=DIRECTION('',(0.,0.,1.));
#6529=FACE_OUTER_BOUND('',#6530,.T.);
#6530=EDGE_LOOP('',(#6531,#6541,#6551,#6561));
#6531=ORIENTED_EDGE('',*,*,#6513,.T.);
#6541=ORIENTED_EDGE('',*,*,#6375,.T.);
#6552=EDGE_CURVE('',#6445,#6366,#6557,.T.);
#6557=LINE('',#6446,#6559);
#6559=VECTOR('',#6560,1.3208);
#6560=DIRECTION('',(0.0,-1.0,0.0));
#6551=ORIENTED_EDGE('',*,*,#6552,.F.);
#6561=ORIENTED_EDGE('',*,*,#6444,.T.);
#6571=STYLED_ITEM('',(#34),#6572);
#6572=ADVANCED_FACE('',(#6578),#6573,.T.);
#6573=PLANE('',#6574);
#6574=AXIS2_PLACEMENT_3D('',#6575,#6576,#6577);
#6575=CARTESIAN_POINT('',(7.239,-0.66,3.048));
#6576=DIRECTION('',(0.0,0.0,1.0));
#6577=DIRECTION('',(0.,1.,0.));
#6578=FACE_OUTER_BOUND('',#6579,.T.);
#6579=EDGE_LOOP('',(#6580,#6590,#6600,#6610));
#6580=ORIENTED_EDGE('',*,*,#6365,.T.);
#6590=ORIENTED_EDGE('',*,*,#6060,.T.);
#6600=ORIENTED_EDGE('',*,*,#6454,.T.);
#6610=ORIENTED_EDGE('',*,*,#6552,.T.);
ENDSEC;
END-ISO-10303-21;
