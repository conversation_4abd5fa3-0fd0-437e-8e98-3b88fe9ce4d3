#!/usr/bin/env python3
"""
Test all parts from the spreadsheet to verify no errors remain
"""

import pandas as pd
import sys
import traceback
import threading
import time
from pathlib import Path

def test_all_spreadsheet_parts():
    """Test processing all parts from the actual spreadsheet"""
    print("🚀 Testing ALL parts from your spreadsheet...")
    print("=" * 60)
    
    try:
        # Load the Excel file
        excel_file = "Teledyne_Flir_master-footprint_list-tura.xlsx"
        df = pd.read_excel(excel_file)
        
        print(f"📊 Loaded Excel: {len(df)} rows, {len(df.columns)} columns")
        
        # Extract all valid parts
        manufacturer_col = 'Manufacturer Name '
        part_col = 'Manufacturer full part number'
        
        parts = []
        for idx, row in df.iterrows():
            mfg = str(row[manufacturer_col]).strip() if pd.notna(row[manufacturer_col]) else ""
            part = str(row[part_col]).strip() if pd.notna(row[part_col]) else ""
            
            if mfg and part and mfg != 'nan' and part != 'nan':
                parts.append({
                    'row': idx + 1,
                    'manufacturer': mfg,
                    'part_number': part
                })
        
        print(f"✅ Found {len(parts)} valid parts to test")
        print("\n📋 All parts:")
        for part in parts:
            print(f"   Row {part['row']:2d}: {part['manufacturer']} {part['part_number']}")
        
        # Test each part with the external datasheet finder
        print(f"\n🔍 Testing each part with external datasheet finder...")
        print("=" * 60)
        
        results = []
        for i, part in enumerate(parts, 1):
            print(f"\n🧪 Test {i}/{len(parts)}: {part['manufacturer']} {part['part_number']}")
            
            try:
                # Import the external finder
                from datasheet_finder import find_datasheet_combined
                
                # Test with timeout
                result = [None]
                exception = [None]
                
                def run_test():
                    try:
                        result[0] = find_datasheet_combined(
                            part['manufacturer'], 
                            part['part_number'], 
                            interactive=False
                        )
                    except Exception as e:
                        exception[0] = e
                
                # Run with timeout
                thread = threading.Thread(target=run_test)
                thread.daemon = True
                thread.start()
                
                # Wait up to 30 seconds per part
                timeout = 30
                for j in range(timeout):
                    if not thread.is_alive():
                        break
                    time.sleep(1)
                    if j % 10 == 9:  # Print every 10 seconds
                        print(f"   ⏳ Still processing... ({j+1}s)")
                
                if thread.is_alive():
                    print(f"   ❌ TIMEOUT after {timeout}s")
                    results.append({
                        'part': part,
                        'status': 'TIMEOUT',
                        'success': False,
                        'message': f'Timeout after {timeout}s'
                    })
                elif exception[0]:
                    print(f"   ❌ ERROR: {exception[0]}")
                    results.append({
                        'part': part,
                        'status': 'ERROR',
                        'success': False,
                        'message': str(exception[0])
                    })
                elif result[0]:
                    success = result[0].get('success', False)
                    message = result[0].get('message', 'No message')
                    source = result[0].get('source', 'Unknown')
                    
                    if success:
                        print(f"   ✅ SUCCESS via {source}")
                    else:
                        print(f"   ⚠️ NO DATASHEET FOUND: {message}")
                    
                    results.append({
                        'part': part,
                        'status': 'SUCCESS' if success else 'NO_DATASHEET',
                        'success': success,
                        'message': message,
                        'source': source
                    })
                else:
                    print(f"   ❌ NO RESULT")
                    results.append({
                        'part': part,
                        'status': 'NO_RESULT',
                        'success': False,
                        'message': 'No result returned'
                    })
                    
            except Exception as e:
                print(f"   ❌ EXCEPTION: {e}")
                results.append({
                    'part': part,
                    'status': 'EXCEPTION',
                    'success': False,
                    'message': str(e)
                })
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 FINAL RESULTS:")
        print("=" * 60)
        
        success_count = sum(1 for r in results if r['success'])
        timeout_count = sum(1 for r in results if r['status'] == 'TIMEOUT')
        error_count = sum(1 for r in results if r['status'] in ['ERROR', 'EXCEPTION'])
        no_datasheet_count = sum(1 for r in results if r['status'] == 'NO_DATASHEET')
        
        print(f"✅ Successful datasheets found: {success_count}/{len(parts)}")
        print(f"⚠️ No datasheets available: {no_datasheet_count}/{len(parts)}")
        print(f"❌ Timeouts: {timeout_count}/{len(parts)}")
        print(f"❌ Errors: {error_count}/{len(parts)}")
        
        if timeout_count > 0 or error_count > 0:
            print(f"\n❌ PROBLEMS FOUND:")
            for result in results:
                if result['status'] in ['TIMEOUT', 'ERROR', 'EXCEPTION']:
                    part = result['part']
                    print(f"   Row {part['row']}: {part['manufacturer']} {part['part_number']} - {result['status']}: {result['message']}")
        
        if success_count > 0:
            print(f"\n✅ SUCCESSFUL PARTS:")
            for result in results:
                if result['success']:
                    part = result['part']
                    source = result.get('source', 'Unknown')
                    print(f"   Row {part['row']}: {part['manufacturer']} {part['part_number']} - Found via {source}")
        
        return timeout_count == 0 and error_count == 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 COMPREHENSIVE SPREADSHEET TEST")
    print("Testing all parts from your Excel file with the external datasheet finder")
    print("This will verify that all errors are fixed and the system works end-to-end")
    print()
    
    success = test_all_spreadsheet_parts()
    
    if success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print("✅ No timeouts or errors found")
        print("✅ Ready for production use")
    else:
        print(f"\n❌ ISSUES FOUND!")
        print("❌ Some parts had timeouts or errors")
        print("❌ Need to investigate and fix remaining problems")
